"""Setup script for Hong Kong Legal XML RAG System."""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="hk-legal-rag",
    version="1.0.0",
    author="Hong Kong Legal RAG Team",
    author_email="<EMAIL>",
    description="A comprehensive RAG system for Hong Kong legal documents",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/hk-legal-rag",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Legal Industry",
        "Topic :: Text Processing :: Linguistic",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.9",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0.0",
            "isort>=5.12.0",
            "mypy>=1.7.0",
            "flake8>=6.0.0",
            "pre-commit>=3.6.0",
        ],
        "graph": [
            "neo4j>=5.14.0",
            "py2neo>=2021.2.3",
        ],
        "search": [
            "elasticsearch>=8.11.0",
        ],
        "monitoring": [
            "prometheus-client>=0.19.0",
            "sentry-sdk>=1.38.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "hk-legal-rag=src.cli:cli",
            "hk-legal-api=src.api.main:app",
            "hk-legal-app=src.app:main",
        ],
    },
    include_package_data=True,
    package_data={
        "src": ["*.yaml", "*.json", "*.txt"],
    },
    zip_safe=False,
)
