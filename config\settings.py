"""
Configuration settings for the Legal Advisor RAG System
"""
import os
from pathlib import Path
from typing import Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """Application settings"""
    
    # Project paths
    PROJECT_ROOT: Path = Path(__file__).parent.parent
    DATA_DIR: Path = PROJECT_ROOT / "data"
    RAW_DATA_DIR: Path = DATA_DIR / "raw"
    PROCESSED_DATA_DIR: Path = DATA_DIR / "processed"
    VECTORSTORE_DIR: Path = DATA_DIR / "vectorstore"
    
    # XML Data paths
    XML_LEGISLATION_DIR: Path = PROJECT_ROOT / "hkel_c_leg_cap_1_cap_300_en"
    XML_INSTRUMENTS_DIR: Path = PROJECT_ROOT / "hkel_c_instruments_en"
    XML_DICTIONARY_FILE: Path = PROJECT_ROOT / "hkel_data-dictionary_en.txt"
    
    # LLM Configuration
    OPENAI_API_KEY: Optional[str] = Field(default=None, env="OPENAI_API_KEY")
    ANTHROPIC_API_KEY: Optional[str] = Field(default=None, env="ANTHROPIC_API_KEY")
    DEFAULT_LLM_MODEL: str = "gpt-4"
    DEFAULT_EMBEDDING_MODEL: str = "text-embedding-ada-002"
    
    # Vector Store Configuration
    VECTORSTORE_TYPE: str = "chroma"  # chroma, faiss, pinecone
    CHUNK_SIZE: int = 1000
    CHUNK_OVERLAP: int = 200
    
    # Search Configuration
    MAX_SEARCH_RESULTS: int = 10
    SIMILARITY_THRESHOLD: float = 0.7
    
    # API Configuration
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_RELOAD: bool = True
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global settings instance
settings = Settings()

# Ensure directories exist
for directory in [
    settings.DATA_DIR,
    settings.RAW_DATA_DIR,
    settings.PROCESSED_DATA_DIR,
    settings.VECTORSTORE_DIR,
]:
    directory.mkdir(parents=True, exist_ok=True)
