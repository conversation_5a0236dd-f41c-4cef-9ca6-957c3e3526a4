"""Middleware components for Hong Kong Legal RAG Backend Service."""

import json
import time
from collections import defaultdict
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Optional

from fastapi import HTTPException, Request, Response, status
from fastapi.middleware.base import BaseHTTPMiddleware
from starlette.middleware.base import RequestResponseEndpoint

from ..config import settings
from .auth import audit_logger


class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware for API protection."""
    
    def __init__(self, app, requests_per_window: int = None, window_seconds: int = None):
        super().__init__(app)
        self.requests_per_window = requests_per_window or settings.rate_limit_requests
        self.window_seconds = window_seconds or settings.rate_limit_window
        
        # In-memory storage (use Redis in production)
        self.request_counts: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        self.window_start_times: Dict[str, datetime] = {}
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Process request with rate limiting."""
        client_ip = self._get_client_ip(request)
        current_time = datetime.utcnow()
        
        # Get or create window for this IP
        window_key = f"{client_ip}_{int(current_time.timestamp() // self.window_seconds)}"
        
        # Clean old windows
        self._cleanup_old_windows(current_time)
        
        # Check rate limit
        if self._is_rate_limited(client_ip, current_time):
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail={
                    "error": "Rate limit exceeded",
                    "limit": self.requests_per_window,
                    "window_seconds": self.window_seconds,
                    "retry_after": self._get_retry_after(client_ip, current_time)
                }
            )
        
        # Increment request count
        self.request_counts[client_ip][window_key] += 1
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        remaining = max(0, self.requests_per_window - self.request_counts[client_ip][window_key])
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_window)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int((current_time + timedelta(seconds=self.window_seconds)).timestamp()))
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address."""
        # Check for forwarded headers (when behind proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    def _is_rate_limited(self, client_ip: str, current_time: datetime) -> bool:
        """Check if client is rate limited."""
        window_key = f"{client_ip}_{int(current_time.timestamp() // self.window_seconds)}"
        current_count = self.request_counts[client_ip][window_key]
        return current_count >= self.requests_per_window
    
    def _get_retry_after(self, client_ip: str, current_time: datetime) -> int:
        """Get retry after seconds."""
        window_start = int(current_time.timestamp() // self.window_seconds) * self.window_seconds
        window_end = window_start + self.window_seconds
        return int(window_end - current_time.timestamp())
    
    def _cleanup_old_windows(self, current_time: datetime):
        """Clean up old rate limit windows."""
        cutoff_time = current_time - timedelta(seconds=self.window_seconds * 2)
        cutoff_timestamp = int(cutoff_time.timestamp() // self.window_seconds)
        
        for client_ip in list(self.request_counts.keys()):
            for window_key in list(self.request_counts[client_ip].keys()):
                window_timestamp = int(window_key.split("_")[-1])
                if window_timestamp < cutoff_timestamp:
                    del self.request_counts[client_ip][window_key]
            
            # Remove empty client entries
            if not self.request_counts[client_ip]:
                del self.request_counts[client_ip]


class AuditLoggingMiddleware(BaseHTTPMiddleware):
    """Audit logging middleware for legal compliance."""
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Process request with audit logging."""
        if not settings.enable_audit_logging:
            return await call_next(request)
        
        start_time = time.time()
        
        # Extract request information
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get("User-Agent", "")
        method = request.method
        url = str(request.url)
        
        # Extract user information if available
        user_id = "anonymous"
        try:
            auth_header = request.headers.get("Authorization")
            if auth_header and auth_header.startswith("Bearer "):
                from .auth import auth_service
                token = auth_header.split(" ")[1]
                token_data = auth_service.verify_token(token)
                user_id = token_data.user_id
        except Exception:
            pass  # Continue with anonymous user
        
        # Process request
        response = await call_next(request)
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Log the request
        audit_logger.log_access(
            user_id=user_id,
            action=f"{method} {url}",
            resource=request.url.path,
            ip_address=client_ip,
            user_agent=user_agent,
            success=response.status_code < 400
        )
        
        # Add audit headers
        response.headers["X-Request-ID"] = f"req_{int(start_time)}_{hash(url) % 10000}"
        response.headers["X-Processing-Time"] = f"{processing_time:.3f}s"
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address."""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Security headers middleware for enhanced protection."""
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Add security headers to response."""
        response = await call_next(request)
        
        # Security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = "default-src 'self'"
        
        # Legal-specific headers
        response.headers["X-Legal-System"] = "Hong Kong"
        response.headers["X-Jurisdiction"] = "HKSAR"
        response.headers["X-Data-Classification"] = "Legal"
        
        return response


class RequestValidationMiddleware(BaseHTTPMiddleware):
    """Request validation middleware for legal queries."""
    
    def __init__(self, app):
        super().__init__(app)
        self.max_query_length = settings.max_legal_query_length
        self.blocked_patterns = [
            # Patterns that might indicate malicious intent
            r"<script",
            r"javascript:",
            r"data:text/html",
            r"vbscript:",
            # SQL injection patterns
            r"union\s+select",
            r"drop\s+table",
            r"delete\s+from",
            # Command injection patterns
            r";\s*rm\s+",
            r";\s*cat\s+",
            r"&&\s*rm\s+",
        ]
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Validate request before processing."""
        # Skip validation for non-API endpoints
        if not request.url.path.startswith("/api/"):
            return await call_next(request)
        
        # Validate request size
        content_length = request.headers.get("Content-Length")
        if content_length and int(content_length) > 10 * 1024 * 1024:  # 10MB limit
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail="Request too large"
            )
        
        # Validate content type for POST requests
        if request.method in ["POST", "PUT", "PATCH"]:
            content_type = request.headers.get("Content-Type", "")
            if not content_type.startswith("application/json"):
                raise HTTPException(
                    status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
                    detail="Content-Type must be application/json"
                )
        
        # For legal query endpoints, validate query content
        if request.url.path in ["/api/legal/query", "/api/legal/chat"]:
            try:
                body = await request.body()
                if body:
                    data = json.loads(body)
                    query = data.get("query", "")
                    
                    # Validate query length
                    if len(query) > self.max_query_length:
                        raise HTTPException(
                            status_code=status.HTTP_400_BAD_REQUEST,
                            detail=f"Query too long. Maximum length: {self.max_query_length}"
                        )
                    
                    # Check for malicious patterns
                    import re
                    for pattern in self.blocked_patterns:
                        if re.search(pattern, query, re.IGNORECASE):
                            raise HTTPException(
                                status_code=status.HTTP_400_BAD_REQUEST,
                                detail="Query contains potentially malicious content"
                            )
                
                # Recreate request with body for downstream processing
                from starlette.requests import Request as StarletteRequest
                
                async def receive():
                    return {"type": "http.request", "body": body}
                
                request._receive = receive
                
            except json.JSONDecodeError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid JSON in request body"
                )
        
        return await call_next(request)


class LegalComplianceMiddleware(BaseHTTPMiddleware):
    """Legal compliance middleware for data protection and privacy."""
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Ensure legal compliance for all requests."""
        # Add compliance tracking
        request.state.compliance_tracking = {
            "data_classification": "legal",
            "jurisdiction": "Hong Kong",
            "privacy_level": "standard",
            "retention_period": "7_years",  # Standard legal document retention
            "access_logged": True
        }
        
        # Process request
        response = await call_next(request)
        
        # Add compliance headers
        response.headers["X-Data-Retention"] = "7-years"
        response.headers["X-Privacy-Policy"] = "https://legal-rag.hk/privacy"
        response.headers["X-Terms-Of-Service"] = "https://legal-rag.hk/terms"
        response.headers["X-Jurisdiction"] = "Hong Kong SAR"
        
        # Add data classification based on endpoint
        if "/legal/" in request.url.path:
            response.headers["X-Data-Classification"] = "Legal-Confidential"
        else:
            response.headers["X-Data-Classification"] = "Public"
        
        return response
