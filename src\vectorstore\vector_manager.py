"""
Vector Store Manager for Legal Documents

This module manages the vector database for storing and retrieving
legal document embeddings with metadata.
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import json

import chromadb
from chromadb.config import Settings
from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import Chroma
from langchain.schema import Document

from ..processors.document_processor import ProcessedChunk, ProcessedDocument
from config.settings import settings

logger = logging.getLogger(__name__)


class LegalVectorStore:
    """Vector store manager for legal documents"""
    
    def __init__(self, 
                 collection_name: str = "legal_documents",
                 persist_directory: Optional[str] = None,
                 embedding_model: str = "text-embedding-ada-002"):
        
        self.collection_name = collection_name
        self.persist_directory = persist_directory or str(settings.VECTORSTORE_DIR)
        self.embedding_model = embedding_model
        
        # Initialize embeddings
        self.embeddings = OpenAIEmbeddings(
            model=embedding_model,
            openai_api_key=settings.OPENAI_API_KEY
        )
        
        # Initialize Chroma client
        self.client = chromadb.PersistentClient(
            path=self.persist_directory,
            settings=Settings(anonymized_telemetry=False)
        )
        
        # Initialize vector store
        self.vectorstore = None
        self._initialize_vectorstore()
    
    def _initialize_vectorstore(self):
        """Initialize the vector store"""
        try:
            self.vectorstore = Chroma(
                collection_name=self.collection_name,
                embedding_function=self.embeddings,
                persist_directory=self.persist_directory,
                client=self.client
            )
            logger.info(f"Initialized vector store: {self.collection_name}")
        except Exception as e:
            logger.error(f"Failed to initialize vector store: {e}")
            raise
    
    def add_documents(self, processed_docs: List[ProcessedDocument]) -> int:
        """Add processed documents to the vector store"""
        total_added = 0
        
        for doc in processed_docs:
            try:
                added_count = self.add_document(doc)
                total_added += added_count
                logger.info(f"Added {added_count} chunks from document: {doc.title}")
            except Exception as e:
                logger.error(f"Failed to add document {doc.title}: {e}")
                continue
        
        logger.info(f"Total chunks added to vector store: {total_added}")
        return total_added
    
    def add_document(self, document: ProcessedDocument) -> int:
        """Add a single processed document to the vector store"""
        if not document.chunks:
            logger.warning(f"No chunks to add for document: {document.title}")
            return 0
        
        # Convert chunks to Langchain documents
        langchain_docs = []
        for chunk in document.chunks:
            doc = self._chunk_to_langchain_doc(chunk, document)
            langchain_docs.append(doc)
        
        # Add to vector store
        try:
            ids = [chunk.id for chunk in document.chunks]
            self.vectorstore.add_documents(langchain_docs, ids=ids)
            return len(langchain_docs)
        except Exception as e:
            logger.error(f"Failed to add chunks to vector store: {e}")
            raise
    
    def _chunk_to_langchain_doc(self, chunk: ProcessedChunk, document: ProcessedDocument) -> Document:
        """Convert a ProcessedChunk to a Langchain Document"""
        # Prepare metadata for vector store
        metadata = {
            'chunk_id': chunk.id,
            'document_id': chunk.parent_document_id,
            'document_title': document.title,
            'chunk_type': chunk.chunk_type,
            'section_hierarchy': json.dumps(chunk.section_hierarchy),
            'section_id': chunk.section_id,
            'legal_citations': json.dumps(chunk.legal_citations),
            'cross_references': json.dumps(chunk.cross_references),
        }
        
        # Add document metadata
        if chunk.metadata:
            for key, value in chunk.metadata.items():
                if isinstance(value, (str, int, float, bool)):
                    metadata[f"doc_{key}"] = value
                elif value is not None:
                    metadata[f"doc_{key}"] = str(value)
        
        return Document(
            page_content=chunk.content,
            metadata=metadata
        )
    
    def search_similar(self, 
                      query: str, 
                      k: int = 10,
                      filter_dict: Optional[Dict[str, Any]] = None,
                      score_threshold: Optional[float] = None) -> List[Tuple[Document, float]]:
        """Search for similar documents"""
        try:
            if score_threshold is not None:
                results = self.vectorstore.similarity_search_with_score(
                    query, k=k, filter=filter_dict
                )
                # Filter by score threshold
                filtered_results = [(doc, score) for doc, score in results if score >= score_threshold]
                return filtered_results
            else:
                results = self.vectorstore.similarity_search_with_score(
                    query, k=k, filter=filter_dict
                )
                return results
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []
    
    def search_by_metadata(self, 
                          metadata_filter: Dict[str, Any], 
                          k: int = 10) -> List[Document]:
        """Search documents by metadata filters"""
        try:
            # Use Chroma's where clause for metadata filtering
            collection = self.client.get_collection(self.collection_name)
            results = collection.query(
                where=metadata_filter,
                n_results=k
            )
            
            # Convert results to Langchain Documents
            documents = []
            for i in range(len(results['documents'][0])):
                doc = Document(
                    page_content=results['documents'][0][i],
                    metadata=results['metadatas'][0][i]
                )
                documents.append(doc)
            
            return documents
        except Exception as e:
            logger.error(f"Metadata search failed: {e}")
            return []
    
    def get_document_by_id(self, chunk_id: str) -> Optional[Document]:
        """Get a specific document by chunk ID"""
        try:
            collection = self.client.get_collection(self.collection_name)
            results = collection.get(ids=[chunk_id])
            
            if results['documents']:
                return Document(
                    page_content=results['documents'][0],
                    metadata=results['metadatas'][0]
                )
            return None
        except Exception as e:
            logger.error(f"Failed to get document by ID {chunk_id}: {e}")
            return None
    
    def delete_document(self, document_id: str) -> bool:
        """Delete all chunks belonging to a document"""
        try:
            # Find all chunks for this document
            collection = self.client.get_collection(self.collection_name)
            results = collection.query(
                where={"document_id": document_id},
                n_results=1000  # Assume max 1000 chunks per document
            )
            
            if results['ids']:
                chunk_ids = results['ids'][0]
                collection.delete(ids=chunk_ids)
                logger.info(f"Deleted {len(chunk_ids)} chunks for document {document_id}")
                return True
            
            return False
        except Exception as e:
            logger.error(f"Failed to delete document {document_id}: {e}")
            return False
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics about the collection"""
        try:
            collection = self.client.get_collection(self.collection_name)
            count = collection.count()
            
            # Get sample of metadata to analyze
            sample_results = collection.query(n_results=min(100, count))
            
            # Analyze chunk types
            chunk_types = {}
            document_types = {}
            
            if sample_results['metadatas']:
                for metadata in sample_results['metadatas'][0]:
                    chunk_type = metadata.get('chunk_type', 'unknown')
                    chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1
                    
                    doc_type = metadata.get('doc_doc_type', 'unknown')
                    document_types[doc_type] = document_types.get(doc_type, 0) + 1
            
            return {
                'total_chunks': count,
                'chunk_types': chunk_types,
                'document_types': document_types,
                'collection_name': self.collection_name,
                'embedding_model': self.embedding_model
            }
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {}
    
    def clear_collection(self) -> bool:
        """Clear all documents from the collection"""
        try:
            # Delete the collection and recreate it
            self.client.delete_collection(self.collection_name)
            self._initialize_vectorstore()
            logger.info(f"Cleared collection: {self.collection_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to clear collection: {e}")
            return False
    
    def persist(self):
        """Persist the vector store to disk"""
        try:
            if hasattr(self.vectorstore, 'persist'):
                self.vectorstore.persist()
            logger.info("Vector store persisted to disk")
        except Exception as e:
            logger.error(f"Failed to persist vector store: {e}")


class VectorStoreManager:
    """High-level manager for multiple vector stores"""
    
    def __init__(self):
        self.stores: Dict[str, LegalVectorStore] = {}
    
    def get_store(self, collection_name: str) -> LegalVectorStore:
        """Get or create a vector store"""
        if collection_name not in self.stores:
            self.stores[collection_name] = LegalVectorStore(collection_name)
        return self.stores[collection_name]
    
    def create_specialized_stores(self) -> Dict[str, LegalVectorStore]:
        """Create specialized stores for different document types"""
        stores = {
            'ordinances': self.get_store('hk_ordinances'),
            'instruments': self.get_store('hk_instruments'),
            'definitions': self.get_store('hk_definitions'),
            'all_documents': self.get_store('hk_all_legal_docs')
        }
        return stores
