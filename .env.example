# Hong Kong Legal Documents RAG System Configuration

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Configuration (optional)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# LLM Configuration
DEFAULT_LLM_MODEL=gpt-4
DEFAULT_EMBEDDING_MODEL=text-embedding-ada-002

# Application Settings
DEBUG=false
LOG_LEVEL=INFO
LOG_FORMAT=json

# Data Directories
DATA_DIR=data
XML_DIR=hkel_c_leg_cap_1_cap_300_en
CACHE_DIR=cache

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/hklegal

# Vector Database (ChromaDB)
CHROMA_PERSIST_DIR=./chroma_db
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

# Graph Database (Neo4j)
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password

# Search Engine (Elasticsearch)
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX=hk_legal

# Redis Cache
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# Processing Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_WORKERS=4

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=8001
