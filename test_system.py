"""
Test script for the Hong Kong Legal Documents RAG System

This script tests the basic functionality of the system with a sample document.
"""
import sys
from pathlib import Path

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from src.parsers.hklm_parser import HKLMParser
from src.processors.document_processor import DocumentProcessor
from src.chunking.legal_chunker import LegalChunkerFactory
from config.settings import settings

def test_parser():
    """Test the XML parser with a sample document"""
    print("Testing XML Parser...")
    
    parser = HKLMParser()
    
    # Test with the first available XML file
    sample_file = None
    for xml_dir in [settings.XML_LEGISLATION_DIR, settings.XML_INSTRUMENTS_DIR]:
        if xml_dir.exists():
            xml_files = list(xml_dir.rglob("*.xml"))
            if xml_files:
                sample_file = xml_files[0]
                break
    
    if not sample_file:
        print("❌ No XML files found for testing")
        return False
    
    print(f"Testing with file: {sample_file}")
    
    try:
        document = parser.parse_file(sample_file)
        if document:
            print("✅ Parser test successful")
            print(f"   Document: {document.metadata.doc_name}")
            print(f"   Type: {document.metadata.doc_type}")
            print(f"   Sections: {len(document.sections)}")
            print(f"   Definitions: {len(document.definitions)}")
            return document
        else:
            print("❌ Parser returned None")
            return False
    except Exception as e:
        print(f"❌ Parser test failed: {e}")
        return False

def test_processor(document):
    """Test the document processor"""
    print("\nTesting Document Processor...")
    
    try:
        processor = DocumentProcessor()
        processed_doc = processor.process_document(document)
        
        print("✅ Processor test successful")
        print(f"   Document ID: {processed_doc.document_id}")
        print(f"   Title: {processed_doc.title}")
        print(f"   Chunks: {len(processed_doc.chunks)}")
        
        # Show chunk types
        chunk_types = {}
        for chunk in processed_doc.chunks:
            chunk_type = chunk.chunk_type
            chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1
        
        print(f"   Chunk types: {chunk_types}")
        return processed_doc
        
    except Exception as e:
        print(f"❌ Processor test failed: {e}")
        return False

def test_chunker(processed_doc):
    """Test the chunking strategy"""
    print("\nTesting Chunker...")
    
    try:
        config = LegalChunkerFactory.get_default_config()
        chunker = LegalChunkerFactory.create_chunker("hierarchical", config)
        
        chunks = chunker.chunk_document(processed_doc)
        
        print("✅ Chunker test successful")
        print(f"   Original chunks: {len(processed_doc.chunks)}")
        print(f"   Final chunks: {len(chunks)}")
        
        # Show sample chunk
        if chunks:
            sample_chunk = chunks[0]
            print(f"   Sample chunk ID: {sample_chunk.id}")
            print(f"   Sample content length: {len(sample_chunk.content)}")
            print(f"   Sample hierarchy: {sample_chunk.section_hierarchy}")
        
        return chunks
        
    except Exception as e:
        print(f"❌ Chunker test failed: {e}")
        return False

def test_vector_store(processed_doc):
    """Test vector store functionality (requires API key)"""
    print("\nTesting Vector Store...")
    
    if not settings.OPENAI_API_KEY:
        print("⚠️  Skipping vector store test - no OpenAI API key")
        return True
    
    try:
        from src.vectorstore.vector_manager import LegalVectorStore
        
        # Create a test vector store
        vector_store = LegalVectorStore("test_collection")
        
        # Add the document
        added_count = vector_store.add_document(processed_doc)
        
        print("✅ Vector store test successful")
        print(f"   Added chunks: {added_count}")
        
        # Test search
        if added_count > 0:
            results = vector_store.search_similar("interpretation", k=3)
            print(f"   Search results: {len(results)}")
            
            if results:
                print(f"   Top result score: {results[0][1]:.4f}")
        
        # Clean up
        vector_store.clear_collection()
        return True
        
    except Exception as e:
        print(f"❌ Vector store test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Hong Kong Legal Documents RAG System - Test Suite")
    print("=" * 60)
    
    # Test 1: Parser
    document = test_parser()
    if not document:
        print("\n❌ Cannot continue - parser test failed")
        return
    
    # Test 2: Processor
    processed_doc = test_processor(document)
    if not processed_doc:
        print("\n❌ Cannot continue - processor test failed")
        return
    
    # Test 3: Chunker
    chunks = test_chunker(processed_doc)
    if not chunks:
        print("\n❌ Cannot continue - chunker test failed")
        return
    
    # Test 4: Vector Store (optional)
    test_vector_store(processed_doc)
    
    print("\n" + "=" * 60)
    print("✅ Test suite completed!")
    print("\nNext steps:")
    print("1. Set up your OpenAI API key in .env file")
    print("2. Run: python src/main.py process")
    print("3. Run: python src/main.py search -q 'your query'")

if __name__ == "__main__":
    main()
