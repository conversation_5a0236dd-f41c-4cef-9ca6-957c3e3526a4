"""
Document Processor for Hong Kong Legal Documents

This module processes parsed legal documents and prepares them for indexing
in the RAG system, including text cleaning, metadata extraction, and
structure preservation.
"""
import logging
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path

from ..parsers.hklm_parser import LegalDocument, LegalSection, LegalMetadata

logger = logging.getLogger(__name__)


@dataclass
class ProcessedChunk:
    """A processed chunk of legal content ready for indexing"""
    id: str
    content: str
    metadata: Dict[str, Any] = field(default_factory=dict)
    section_hierarchy: List[str] = field(default_factory=list)
    chunk_type: str = "content"  # content, definition, reference, title
    parent_document_id: str = ""
    section_id: Optional[str] = None
    legal_citations: List[str] = field(default_factory=list)
    cross_references: List[str] = field(default_factory=list)


@dataclass
class ProcessedDocument:
    """A fully processed legal document"""
    document_id: str
    title: str
    metadata: Dict[str, Any]
    chunks: List[ProcessedChunk]
    definitions: Dict[str, str]
    cross_references: List[Dict[str, str]]
    document_structure: Dict[str, Any]


class DocumentProcessor:
    """Processes legal documents for RAG indexing"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.citation_pattern = re.compile(r'(?:Cap\.|Chapter|Section|s\.|ss\.|Ordinance)\s*\d+[A-Z]*(?:\([^)]+\))?')
        self.reference_pattern = re.compile(r'<ref[^>]*href="([^"]*)"[^>]*>([^<]*)</ref>')
    
    def process_document(self, document: LegalDocument) -> ProcessedDocument:
        """Process a legal document into chunks ready for indexing"""
        try:
            # Generate document ID
            doc_id = self._generate_document_id(document)
            
            # Extract title
            title = document.short_title or document.long_title or document.metadata.doc_name or "Untitled"
            
            # Prepare metadata
            metadata = self._extract_document_metadata(document)
            
            # Process all chunks
            chunks = []
            
            # Process title as a special chunk
            if document.long_title:
                title_chunk = self._create_title_chunk(document.long_title, doc_id, metadata)
                chunks.append(title_chunk)
            
            # Process definitions
            definition_chunks = self._process_definitions(document.definitions, doc_id, metadata)
            chunks.extend(definition_chunks)
            
            # Process sections hierarchically
            section_chunks = self._process_sections(document.sections, doc_id, metadata)
            chunks.extend(section_chunks)
            
            # Create document structure map
            structure = self._create_document_structure(document)
            
            return ProcessedDocument(
                document_id=doc_id,
                title=title,
                metadata=metadata,
                chunks=chunks,
                definitions=document.definitions,
                cross_references=document.cross_references,
                document_structure=structure
            )
            
        except Exception as e:
            logger.error(f"Error processing document: {e}")
            raise
    
    def _generate_document_id(self, document: LegalDocument) -> str:
        """Generate a unique document ID"""
        if document.metadata.identifier:
            return document.metadata.identifier.replace('/', '_').replace('!', '_')
        elif document.metadata.doc_name:
            return document.metadata.doc_name.replace(' ', '_').replace('.', '_')
        else:
            return f"doc_{hash(str(document.metadata))}"
    
    def _extract_document_metadata(self, document: LegalDocument) -> Dict[str, Any]:
        """Extract and normalize document metadata"""
        metadata = {
            'doc_name': document.metadata.doc_name,
            'doc_type': document.metadata.doc_type,
            'doc_number': document.metadata.doc_number,
            'doc_status': document.metadata.doc_status,
            'identifier': document.metadata.identifier,
            'date': document.metadata.date,
            'subject': document.metadata.subject,
            'language': document.metadata.language,
            'publisher': document.metadata.publisher,
            'rights': document.metadata.rights,
            'long_title': document.long_title,
            'short_title': document.short_title,
            'commencement_note': document.commencement_note,
        }
        
        # Remove None values
        return {k: v for k, v in metadata.items() if v is not None}
    
    def _create_title_chunk(self, title: str, doc_id: str, metadata: Dict[str, Any]) -> ProcessedChunk:
        """Create a chunk for the document title"""
        chunk_id = f"{doc_id}_title"
        
        chunk_metadata = metadata.copy()
        chunk_metadata.update({
            'chunk_type': 'title',
            'section_type': 'title'
        })
        
        return ProcessedChunk(
            id=chunk_id,
            content=title,
            metadata=chunk_metadata,
            section_hierarchy=['title'],
            chunk_type='title',
            parent_document_id=doc_id
        )
    
    def _process_definitions(self, definitions: Dict[str, str], doc_id: str, metadata: Dict[str, Any]) -> List[ProcessedChunk]:
        """Process definitions into chunks"""
        chunks = []
        
        for term, definition in definitions.items():
            chunk_id = f"{doc_id}_def_{term.replace(' ', '_')}"
            
            chunk_metadata = metadata.copy()
            chunk_metadata.update({
                'chunk_type': 'definition',
                'defined_term': term,
                'section_type': 'definition'
            })
            
            content = f"Definition of '{term}': {definition}"
            
            chunk = ProcessedChunk(
                id=chunk_id,
                content=content,
                metadata=chunk_metadata,
                section_hierarchy=['definitions', term],
                chunk_type='definition',
                parent_document_id=doc_id
            )
            
            chunks.append(chunk)
        
        return chunks

    def _process_sections(self, sections: List[LegalSection], doc_id: str, metadata: Dict[str, Any],
                         parent_hierarchy: List[str] = None) -> List[ProcessedChunk]:
        """Process sections hierarchically into chunks"""
        if parent_hierarchy is None:
            parent_hierarchy = []

        chunks = []

        for section in sections:
            # Build section hierarchy
            section_hierarchy = parent_hierarchy.copy()
            if section.num:
                section_hierarchy.append(f"{section.level}_{section.num}")
            elif section.name:
                section_hierarchy.append(f"{section.level}_{section.name}")
            else:
                section_hierarchy.append(section.level)

            # Create chunk for this section
            if section.content or section.heading:
                chunk = self._create_section_chunk(section, doc_id, metadata, section_hierarchy)
                chunks.append(chunk)

            # Process subsections recursively
            if section.subsections:
                subsection_chunks = self._process_sections(
                    section.subsections, doc_id, metadata, section_hierarchy
                )
                chunks.extend(subsection_chunks)

        return chunks

    def _create_section_chunk(self, section: LegalSection, doc_id: str,
                             metadata: Dict[str, Any], hierarchy: List[str]) -> ProcessedChunk:
        """Create a chunk for a legal section"""
        chunk_id = f"{doc_id}_section_{section.id or '_'.join(hierarchy)}"

        # Build content
        content_parts = []
        if section.heading:
            content_parts.append(f"Section: {section.heading}")
        if section.num:
            content_parts.append(f"Number: {section.num}")
        if section.content:
            content_parts.append(section.content)

        content = '\n'.join(content_parts)

        # Extract citations and references
        citations = self._extract_citations(content)
        references = self._extract_references(content)

        # Prepare chunk metadata
        chunk_metadata = metadata.copy()
        chunk_metadata.update({
            'chunk_type': 'content',
            'section_type': section.level,
            'section_id': section.id,
            'section_name': section.name,
            'section_num': section.num,
            'section_heading': section.heading,
            'section_status': section.status,
            'section_reason': section.reason,
            'temporal_id': section.temporal_id,
            'hierarchy_level': len(hierarchy),
            'citations_count': len(citations),
            'references_count': len(references)
        })

        return ProcessedChunk(
            id=chunk_id,
            content=content,
            metadata=chunk_metadata,
            section_hierarchy=hierarchy,
            chunk_type='content',
            parent_document_id=doc_id,
            section_id=section.id,
            legal_citations=citations,
            cross_references=references
        )

    def _extract_citations(self, text: str) -> List[str]:
        """Extract legal citations from text"""
        citations = self.citation_pattern.findall(text)
        return list(set(citations))  # Remove duplicates

    def _extract_references(self, text: str) -> List[str]:
        """Extract cross-references from text"""
        references = self.reference_pattern.findall(text)
        return [ref[1] for ref in references]  # Extract reference text

    def _create_document_structure(self, document: LegalDocument) -> Dict[str, Any]:
        """Create a hierarchical structure map of the document"""
        structure = {
            'metadata': {
                'doc_name': document.metadata.doc_name,
                'doc_type': document.metadata.doc_type,
                'doc_number': document.metadata.doc_number,
                'doc_status': document.metadata.doc_status
            },
            'titles': {
                'long_title': document.long_title,
                'short_title': document.short_title
            },
            'sections': self._build_section_structure(document.sections),
            'definitions_count': len(document.definitions),
            'cross_references_count': len(document.cross_references)
        }

        return structure

    def _build_section_structure(self, sections: List[LegalSection]) -> List[Dict[str, Any]]:
        """Build hierarchical structure for sections"""
        structure = []

        for section in sections:
            section_info = {
                'id': section.id,
                'name': section.name,
                'num': section.num,
                'heading': section.heading,
                'level': section.level,
                'has_content': bool(section.content),
                'subsections': self._build_section_structure(section.subsections) if section.subsections else []
            }
            structure.append(section_info)

        return structure

    def process_multiple_documents(self, documents: List[LegalDocument]) -> List[ProcessedDocument]:
        """Process multiple documents"""
        processed_docs = []

        for i, document in enumerate(documents):
            try:
                processed_doc = self.process_document(document)
                processed_docs.append(processed_doc)
                logger.info(f"Processed document {i+1}/{len(documents)}: {processed_doc.title}")
            except Exception as e:
                logger.error(f"Failed to process document {i+1}: {e}")
                continue

        logger.info(f"Successfully processed {len(processed_docs)}/{len(documents)} documents")
        return processed_docs

    def get_processing_stats(self, processed_docs: List[ProcessedDocument]) -> Dict[str, Any]:
        """Get statistics about processed documents"""
        total_chunks = sum(len(doc.chunks) for doc in processed_docs)
        total_definitions = sum(len(doc.definitions) for doc in processed_docs)
        total_references = sum(len(doc.cross_references) for doc in processed_docs)

        chunk_types = {}
        for doc in processed_docs:
            for chunk in doc.chunks:
                chunk_type = chunk.chunk_type
                chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1

        return {
            'total_documents': len(processed_docs),
            'total_chunks': total_chunks,
            'total_definitions': total_definitions,
            'total_cross_references': total_references,
            'chunk_types': chunk_types,
            'avg_chunks_per_doc': total_chunks / len(processed_docs) if processed_docs else 0
        }
