# Hong Kong Legal Documents RAG System

A comprehensive Retrieval-Augmented Generation (RAG) system for indexing and querying Hong Kong legal documents. This system processes XML legal documents following the Hong Kong Legislation Model (HKLM) schema and provides intelligent search capabilities powered by large language models.

## Features

- **XML Document Parsing**: Comprehensive parser for HKLM XML schema
- **Hierarchical Processing**: Preserves legal document structure and relationships
- **Intelligent Chunking**: Legal-aware chunking strategies that maintain context
- **Vector Search**: Semantic search with metadata filtering
- **Citation Handling**: Automatic extraction and preservation of legal citations
- **Cross-Reference Support**: Maintains document relationships and references
- **Multilingual Support**: Handles English and Chinese legal documents

## Architecture

### Core RAG Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   XML Parser    │───▶│ Document Processor│───▶│ Legal Chunker   │
│  (HKLM Schema)  │    │  (Structure +     │    │ (Hierarchical)  │
└─────────────────┘    │   Metadata)       │    └─────────────────┘
                       └──────────────────┘              │
                                                         ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Search API    │◀───│  Vector Store    │◀───│   Embeddings    │
│  (FastAPI)      │    │ (ChromaDB/Qdrant)│    │   (OpenAI)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Agentic RAG Architecture

The system can be extended with autonomous agent capabilities for advanced legal research workflows:

```
┌─────────────────────────────────────────────────────────────────┐
│                    Agentic RAG Orchestrator                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Research Agent  │  │ Citation Agent  │  │ Analysis Agent  │ │
│  │ (Multi-step     │  │ (Verification & │  │ (Cross-ref &    │ │
│  │  Queries)       │  │  Validation)    │  │  Reasoning)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ LangGraph/ReAct │───▶│   Tool Registry  │───▶│ Function Calling│
│   Framework     │    │ (Legal Tools)    │    │   Agents        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Core RAG System                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Vector Store    │  │ Document Store  │  │ Metadata Index  │ │
│  │(Qdrant/Chroma)  │  │   (Legal Docs)  │  │ (Citations)     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd LegalAdvisor

# Install dependencies
pip install -r requirements.txt

# Copy environment configuration
cp .env.example .env
```

### 2. Configuration

Edit `.env` file with your API keys:

```bash
OPENAI_API_KEY=your_openai_api_key_here
```

### 3. Test the System

```bash
# Run basic functionality tests
python test_system.py
```

### 4. Process Documents

```bash
# Process all XML documents and build vector index
python src/main.py process

# Process specific directory
python src/main.py process -d /path/to/xml/documents

# Use custom collection name
python src/main.py process -c my_legal_docs
```

### 5. Search Documents

```bash
# Basic search
python src/main.py search -q "interpretation of contracts"

# Search with more results
python src/main.py search -q "criminal procedure" -l 10

# Search specific collection
python src/main.py search -q "employment law" -c my_legal_docs
```

## Project Structure

```
LegalAdvisor/
├── src/
│   ├── parsers/
│   │   └── hklm_parser.py          # XML parser for HKLM schema
│   ├── processors/
│   │   └── document_processor.py   # Document processing and metadata extraction
│   ├── chunking/
│   │   └── legal_chunker.py        # Legal-aware chunking strategies
│   ├── vectorstore/
│   │   └── vector_manager.py       # Vector database management
│   └── main.py                     # Main CLI interface
├── config/
│   └── settings.py                 # Configuration management
├── data/                           # Data directories (auto-created)
│   ├── raw/
│   ├── processed/
│   └── vectorstore/
├── hkel_c_leg_cap_1_cap_300_en/   # XML legal documents (Ordinances)
├── hkel_c_instruments_en/         # XML legal documents (Instruments)
├── hkel_data-dictionary_en.txt    # HKLM schema documentation
├── requirements.txt               # Python dependencies
├── test_system.py                # System validation tests
└── README.md                     # This file
```

## Document Types Supported

- **Ordinances**: Primary legislation (Cap. 1-300+)
- **Subsidiary Legislation**: Regulations, rules, orders
- **Instruments**: Constitutional and administrative documents
- **Amendments**: Legislative amendments and modifications

## Technology Stack

### Core Technologies
- **Python 3.9+**: Primary development language
- **LangChain**: RAG orchestration and document processing
- **OpenAI GPT-4**: Large language model for reasoning
- **OpenAI Embeddings**: Text embedding generation

### Vector Databases
The system supports multiple vector database options:

#### ChromaDB (Default)
- **Pros**: Lightweight, easy setup, good for development and small-scale deployments
- **Cons**: Limited scalability for very large document collections
- **Best for**: Development, prototyping, small to medium legal practices
- **Legal Use Case**: Ideal for single-jurisdiction legal research with moderate document volumes

#### Qdrant (Recommended for Production)
- **Pros**: High performance, advanced filtering, better scalability, production-ready
- **Cons**: More complex setup, requires additional infrastructure
- **Best for**: Large-scale legal databases, multi-tenant systems, enterprise deployments
- **Legal Use Case**: Perfect for comprehensive legal research platforms with millions of documents

### Agent Frameworks
- **LangGraph**: State-based agent workflows for complex legal research chains
- **ReAct**: Reasoning and acting framework for iterative legal analysis
- **Function Calling Agents**: Tool-based agents for specific legal tasks
- **Multi-Agent Systems**: Collaborative agents for comprehensive legal research

### Additional Technologies
- **FastAPI**: REST API framework
- **Streamlit**: Rapid UI prototyping
- **lxml/BeautifulSoup**: XML parsing for HKLM documents
- **spaCy/NLTK**: Natural language processing
- **Docker**: Containerization and deployment

## Agentic RAG Capabilities

### Multi-Step Legal Research Workflows

The agentic system enables sophisticated legal research through autonomous agent chains:

1. **Research Planning Agent**
   - Analyzes complex legal queries
   - Breaks down multi-faceted questions
   - Plans optimal research strategies
   - Identifies relevant legal domains

2. **Document Retrieval Agent**
   - Executes targeted searches across document collections
   - Applies contextual filters (jurisdiction, date, document type)
   - Performs iterative refinement based on initial results
   - Maintains search history for optimization

3. **Citation Verification Agent**
   - Validates legal citations automatically
   - Checks cross-references between documents
   - Identifies outdated or superseded provisions
   - Ensures citation accuracy and completeness

4. **Cross-Reference Analysis Agent**
   - Maps relationships between legal provisions
   - Identifies conflicting or complementary laws
   - Traces legislative history and amendments
   - Builds comprehensive legal context networks

5. **Legal Reasoning Agent**
   - Applies legal principles to specific scenarios
   - Constructs logical argument chains
   - Identifies precedents and analogies
   - Synthesizes findings into coherent legal advice

### Agentic Workflow Examples

#### Complex Legal Query Resolution
```
User Query: "What are the employment termination requirements for foreign workers in Hong Kong?"

Agent Workflow:
1. Research Agent → Identifies relevant ordinances (Employment, Immigration)
2. Retrieval Agent → Searches for termination provisions, foreign worker regulations
3. Citation Agent → Verifies current status of identified provisions
4. Analysis Agent → Cross-references employment and immigration requirements
5. Reasoning Agent → Synthesizes comprehensive answer with step-by-step requirements
```

#### Autonomous Legal Document Analysis
```
New Document Processing:
1. Classification Agent → Identifies document type and legal domain
2. Impact Analysis Agent → Assesses effects on existing legal framework
3. Citation Mapping Agent → Updates cross-reference networks
4. Conflict Detection Agent → Identifies potential legal conflicts
5. Summary Agent → Generates executive summary of changes
```

### Agent Tool Registry

The system provides specialized tools for legal agents:

- **Citation Validator**: Verifies legal citation formats and validity
- **Cross-Reference Mapper**: Builds and maintains document relationship graphs
- **Legal Timeline Builder**: Constructs chronological legal development chains
- **Precedent Finder**: Identifies relevant case law and precedents
- **Amendment Tracker**: Monitors and applies legislative changes
- **Jurisdiction Filter**: Applies location-specific legal constraints

## Key Components

### XML Parser (`hklm_parser.py`)
- Parses HKLM XML schema documents
- Extracts metadata, structure, and content
- Handles cross-references and citations
- Preserves legal hierarchy

### Document Processor (`document_processor.py`)
- Converts parsed documents to searchable format
- Extracts and normalizes metadata
- Creates structured chunks with context
- Handles definitions and references

### Legal Chunker (`legal_chunker.py`)
- Hierarchical chunking that respects legal structure
- Preserves section boundaries and relationships
- Creates overlapping chunks for context
- Handles special content types (definitions, titles)

### Vector Store (`vector_manager.py`)
- ChromaDB integration for vector storage
- Metadata filtering and search
- Document management and statistics
- Persistent storage

## CLI Commands

### Process Documents
```bash
python src/main.py process [OPTIONS]

Options:
  -d, --directory PATH    Directory containing XML documents
  -c, --collection TEXT   Vector store collection name
```

### Search Documents
```bash
python src/main.py search [OPTIONS]

Options:
  -q, --query TEXT        Search query (required)
  -c, --collection TEXT   Collection to search
  -l, --limit INTEGER     Number of results
```

### View Statistics
```bash
python src/main.py stats [OPTIONS]

Options:
  -c, --collection TEXT   Collection name
```

### Clear Collection
```bash
python src/main.py clear [OPTIONS]

Options:
  -c, --collection TEXT   Collection to clear
```

## Configuration Options

Key settings in `config/settings.py`:

- `CHUNK_SIZE`: Maximum chunk size (default: 1000)
- `CHUNK_OVERLAP`: Overlap between chunks (default: 200)
- `MAX_SEARCH_RESULTS`: Maximum search results (default: 10)
- `SIMILARITY_THRESHOLD`: Minimum similarity score (default: 0.7)

### Vector Database Configuration

#### ChromaDB Configuration (Default)
```python
VECTORSTORE_TYPE = "chroma"
CHROMA_PERSIST_DIRECTORY = "./data/vectorstore"
CHROMA_COLLECTION_NAME = "legal_documents"
```

#### Qdrant Configuration (Production)
```python
VECTORSTORE_TYPE = "qdrant"
QDRANT_HOST = "localhost"
QDRANT_PORT = 6333
QDRANT_API_KEY = "your_qdrant_api_key"  # For Qdrant Cloud
QDRANT_COLLECTION_NAME = "legal_documents"
```

### Agentic RAG Configuration
```python
# Agent Framework Settings
AGENT_FRAMEWORK = "langgraph"  # langgraph, react, function_calling
MAX_AGENT_ITERATIONS = 10
AGENT_TIMEOUT_SECONDS = 300

# Multi-Agent Settings
ENABLE_MULTI_AGENT = True
RESEARCH_AGENT_MODEL = "gpt-4"
CITATION_AGENT_MODEL = "gpt-3.5-turbo"
ANALYSIS_AGENT_MODEL = "gpt-4"

# Tool Configuration
ENABLE_CITATION_VALIDATION = True
ENABLE_CROSS_REFERENCE_ANALYSIS = True
ENABLE_LEGAL_REASONING_CHAINS = True
```

## Implementing Agentic RAG

### Quick Start with Agentic Features

1. **Install Additional Dependencies**
```bash
pip install langgraph langchain-experimental qdrant-client
```

2. **Configure Agent Framework**
```python
# config/agent_settings.py
AGENT_CONFIG = {
    "framework": "langgraph",
    "max_iterations": 10,
    "tools": ["citation_validator", "cross_ref_analyzer", "legal_reasoner"]
}
```

3. **Initialize Agentic System**
```python
from src.agents.legal_research_agent import LegalResearchOrchestrator

# Create agentic RAG system
orchestrator = LegalResearchOrchestrator(
    vector_store=vector_store,
    llm_model="gpt-4",
    agent_config=AGENT_CONFIG
)

# Execute complex legal research
result = orchestrator.research(
    query="Analyze employment termination requirements for foreign workers",
    research_depth="comprehensive",
    include_citations=True,
    verify_currency=True
)
```

### Agent Implementation Example

```python
# src/agents/legal_research_agent.py
from langgraph import StateGraph, END
from langchain.agents import AgentExecutor

class LegalResearchOrchestrator:
    def __init__(self, vector_store, llm_model, agent_config):
        self.vector_store = vector_store
        self.llm = llm_model
        self.config = agent_config
        self.workflow = self._build_workflow()

    def _build_workflow(self):
        workflow = StateGraph(ResearchState)

        # Add agent nodes
        workflow.add_node("research_planner", self.plan_research)
        workflow.add_node("document_retriever", self.retrieve_documents)
        workflow.add_node("citation_validator", self.validate_citations)
        workflow.add_node("cross_ref_analyzer", self.analyze_cross_refs)
        workflow.add_node("legal_reasoner", self.apply_legal_reasoning)

        # Define workflow edges
        workflow.add_edge("research_planner", "document_retriever")
        workflow.add_edge("document_retriever", "citation_validator")
        workflow.add_edge("citation_validator", "cross_ref_analyzer")
        workflow.add_edge("cross_ref_analyzer", "legal_reasoner")
        workflow.add_edge("legal_reasoner", END)

        return workflow.compile()
```

## Development

### Adding New Features

1. **Custom Chunking Strategy**: Extend `ChunkingStrategy` class
2. **New Document Types**: Modify `HKLMParser` for additional schemas
3. **Enhanced Search**: Extend `LegalVectorStore` with new search methods
4. **API Integration**: Add endpoints in future API module
5. **Agentic Capabilities**: Implement LangGraph workflows for autonomous legal research
6. **Vector Database Migration**: Switch from ChromaDB to Qdrant for production scalability

### Testing

```bash
# Run system tests
python test_system.py

# Test specific components
python -m pytest tests/  # (when test suite is added)
```

## Troubleshooting

### Common Issues

1. **No XML files found**: Ensure XML documents are in the correct directories
2. **API key errors**: Check `.env` file configuration
3. **Memory issues**: Reduce `CHUNK_SIZE` for large documents
4. **Search returns no results**: Check similarity threshold settings

### Logging

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
python src/main.py process
```

## Agentic RAG Use Cases for Legal Advisors

### 1. Comprehensive Legal Research
**Scenario**: A lawyer needs to research employment law implications for a multinational company.

**Agentic Workflow**:
- Research Agent plans multi-jurisdictional search strategy
- Retrieval Agents search Hong Kong, mainland China, and international employment laws
- Citation Agents verify current status of all referenced laws
- Analysis Agent identifies conflicts and harmonization opportunities
- Reasoning Agent provides jurisdiction-specific recommendations

### 2. Due Diligence Automation
**Scenario**: Legal due diligence for a corporate acquisition.

**Agentic Workflow**:
- Classification Agent categorizes all target company documents
- Compliance Agent checks against regulatory requirements
- Risk Assessment Agent identifies potential legal liabilities
- Cross-Reference Agent maps regulatory dependencies
- Summary Agent generates comprehensive due diligence report

### 3. Regulatory Compliance Monitoring
**Scenario**: Ongoing compliance monitoring for financial services.

**Agentic Workflow**:
- Monitoring Agent tracks regulatory changes in real-time
- Impact Analysis Agent assesses effects on current operations
- Gap Analysis Agent identifies compliance deficiencies
- Recommendation Agent suggests corrective actions
- Reporting Agent generates compliance status updates

### 4. Contract Analysis and Review
**Scenario**: Automated contract review for standard agreements.

**Agentic Workflow**:
- Parsing Agent extracts key contract terms and clauses
- Comparison Agent benchmarks against standard templates
- Risk Agent identifies unusual or problematic provisions
- Precedent Agent finds relevant case law for specific clauses
- Advisory Agent provides revision recommendations

### 5. Legal Research Training and Knowledge Management
**Scenario**: Training junior lawyers and maintaining institutional knowledge.

**Agentic Workflow**:
- Learning Agent identifies knowledge gaps in research queries
- Tutorial Agent provides guided research methodology
- Best Practice Agent suggests optimal search strategies
- Knowledge Capture Agent documents successful research patterns
- Mentoring Agent provides personalized learning recommendations

## Future Enhancements

### Core System Improvements
- [ ] Web UI interface (Streamlit/FastAPI)
- [ ] Multi-language support enhancement
- [ ] Real-time document updates
- [ ] Integration with external legal databases
- [ ] Advanced analytics and reporting

### Agentic RAG Features
- [ ] **Autonomous Legal Document Analysis**
  - Self-improving document classification
  - Automatic legal impact assessment
  - Intelligent document summarization

- [ ] **Multi-Agent Legal Research Teams**
  - Specialized agent roles (researcher, analyst, validator)
  - Collaborative agent workflows
  - Agent performance optimization

- [ ] **Self-Improving Search Strategies**
  - Machine learning-based query optimization
  - Adaptive search result ranking
  - User feedback integration for continuous improvement

- [ ] **Advanced Legal Reasoning Chains**
  - Complex multi-step legal analysis
  - Precedent-based reasoning systems
  - Analogical legal reasoning capabilities

- [ ] **Intelligent Citation Networks**
  - Dynamic citation relationship mapping
  - Automated citation currency checking
  - Predictive citation analysis

- [ ] **Legal Knowledge Graph Construction**
  - Automated legal concept extraction
  - Relationship mapping between legal entities
  - Temporal legal knowledge evolution tracking

- [ ] **Proactive Legal Advisory System**
  - Risk prediction based on legal trends
  - Proactive compliance recommendations
  - Early warning systems for regulatory changes

- [ ] **Cross-Jurisdictional Legal Analysis**
  - Multi-jurisdiction legal comparison
  - Conflict of laws analysis
  - International legal harmonization insights

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the logs for error details
3. Open an issue on GitHub with detailed information
