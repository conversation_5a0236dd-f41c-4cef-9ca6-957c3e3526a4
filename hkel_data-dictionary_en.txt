

--- Page 1 ---

 
 
 
 
 
Department of Justice 
 
Hong Kong e-Legislation – Hong Kong Legislation 
Data Dictionary for Extensible Markup Language 
(XML) 
 
 
 
 
 
 
 
 
Version: 1.0 
October 2019 
 
© The Government of the Hong Kong Special Administrative Region 
The contents of this document remain the property of, and may not be reproduced in whole or in part without 
the express permission of the Government of the Hong Kong Special Administrative Region. 


--- Page 2 ---

Hong Kong Legislation Extensible Markup Language (XML)  
Introduction 
 
Version 1.0 
October 2019 
 1 
 
1. 
Introduction 
Hong Kong Legislation Model (HKLM) is a schema for the modelling of Hong Kong legislation 
in the Extensible Markup Language (XML) format. 
 
1.1. About Data Resource of Hong Kong e-Legislation – Hong Kong Legislation 
 
Resource format: 
XML 
Resource encoding: 
Universal Transformation Format-8 (UTF-8) 
 
File name: 
[cap|a]_[cap_no.(A to ZZ)]_[yyyymmddhhmiss | --------------]_[en|zh-Hant|zh-Hans]_[c|p].xml 
 
Notation 
Description 
[cap|a] 
“cap” for Ordinance & Subsidiary Legislation. 
“a” for Instrument. 
[cap_no.(A to ZZ)] 
cap_no. for chapter number. 
A to ZZ for suffix of Subsidiary Legislation in capital letter. 
[yyyymmddhhmiss | --------] 
Version date. 
yyyy for year (4-digit),  
mm for month (2-digit; 01-12),  
dd for day(2-digit; 01-31),  
hh for hour (2-digit; 00-23),  
mi for minute (2-digit; 00-59),  
ss for second (2-digit; 00-59),  
-------------- for no version date (14). 
[en|zh-Hant|zh-Hans ] 
“en” for English version. 
“zh-Hant” for traditional Chinese version. 
“zh-Hans” for simplified Chinese version. 
[c|p] 
“c” for current version. 
“p” for past version. 
 
 
 
 
 


--- Page 3 ---

Hong Kong Legislation Extensible Markup Language (XML)  
Data Dictionary 
 
Version 1.0 
October 2019 
 
2 
2. Data Dictionary 
2.1. Element 
Element Name 
Element Description 
Remark 
marker 
The <marker> element is a primitive element to be used to mark or denote a spot in 
the text. It can be used in the <content> areas or anywhere else where an <inline> 
element is expected. The <marker> element contains no text. 
 
inline 
The <inline> element is a primitive element to be used within <content> areas or 
within any other areas which can accept inline content. 
 
block 
The <block> element is a primitive element to be used anywhere where <block> 
elements are permitted including within <content> elements or anywhere where 
<block> elements have been explicitly permitted. 
 
content 
The <content> element is a primitive element to be used anywhere where a very 
general content model is desired, including within other <content> elements. 
 
lawDoc 
A <lawDoc> is a base level element representing all types of legislative documents. 
 
document 
A <document> is a base level element for loosely structured documents. 
 
meta 
A <meta> block contains properties and sets of properties recording metadata about 
the document. The information contained within the meta block is not part of the 
official law. 
 
property 
A <property> is simple value recorded with the document. 
 
set 
A <set> is a grouping of properties in the <meta> block. 
 
toc 
A <toc> is a table of contents which can appear in a number of locations in document  
tocItem 
A <tocItem> is an entry in a table of contents. 
 
main 
A <main> is the primary container for the body of a legislative document. 
 
statement 
A <statement> is the general container for the provisions at the beginning of 
legislation. 
 


--- Page 4 ---

Hong Kong Legislation Extensible Markup Language (XML)  
Data Dictionary 
 
Version 1.0 
October 2019 
 
3 
Element Name 
Element Description 
Remark 
preamble 
The <preamble> is a container for the "Whereas" clauses and the enacting formula. 
 
recital 
A <recital> is a preliminary statement in a Bill stating the reasons for the Bill. 
 
enactingFormula 
The <enactingFormula> is clause declaring the source from which the law claims to 
derive its authority. 
 
level 
A <level> is the general container for the main provisions of legislation, often 
organized as a hierarchy. 
 
num 
The <num> surrounds the numeric designation, including the surrounding decoration, 
assigned to a level in legislation. 
 
 
text 
The <text> element is a base class for text that can be interspersed in the hierarchy of 
a document. 
 
heading 
The <heading> represents the heading which is an optional part of a level. 
 
subheading 
The <subheading> represents the subheading under a heading and is an optional part 
of a level. 
 
crossHeading 
A <crossHeading> is a non-hierarchical heading construct which can be placed within 
and amongst heading levels. 
 
instruction 
An <instruction> is a container having formalized text describing an amendment or a 
modification. 
An 
<instruction> 
contains 
the 
actions 
and 
<quotedText>/<quotedStructure> necessary to describe an amendment. 
 
action 
An <action> is an atomic-level change defined within an amending formula. The 
action contains the text related to that action and, through attributes, identifies the item 
affected and the type of action to be performed. 
 
notes 
The <notes> is a container for collections of individual notes. 
 
note 
A <note> is a generic element for associated notes with items in the document. 
 
appendix 
An <appendix> is a generic element appended to the main part of a document. 
 


--- Page 5 ---

Hong Kong Legislation Extensible Markup Language (XML)  
Data Dictionary 
 
Version 1.0 
October 2019 
 
4 
Element Name 
Element Description 
Remark 
ref 
A <ref> element is a reference or link to another document, a location within another 
document, or a location with the same document. 
 
date 
A <date> element is a wrapper around dates. A normalized value of the date text can 
be stored in the @date attribute or in the @startDate and @endDate attributes in the 
case of a date range. 
 
def 
A <def> denotes a definition defining one or more defined terms. 
 
quotedText 
A <quotedText> element is used for an extraction of simple text from another source 
or origin. 
 
quotedStructure 
A <quotedStructure> element is used for an extraction of structured text (text with 
XML elements) from another source or origin. 
 
layout 
A <layout> element is used to denote an area of text intended to be displayed in a 
columns-oriented format similar to table. 
 
header 
A <header> denotes a header row within a column-based structure. 
 
row 
A <row> denotes a row entry within a column-based <layout> structure. 
 
column 
A <column> denotes a column in a column-based <layout> structure. 
 
p 
A <p> is a simple paragraph which is different from the formal paragraph level of 
legislative documents. 
 
br 
A <br> is simple marker element denoting a line break. 
 
img 
An <img> denotes where a graphic image is inserted. 
 
center 
A <center> is content text to be centred on the page. 
 
fillIn 
A <fillIn> is an inline spacer which denotes an area to be filled in a printed form. 
Usually, a <fillin> is rendered as dotted lines with the text content within the <fillIn> 
tags shown just below. If parenthesis are to surround the text shown below the line, 
then those parenthesis should be included in the text content.  
 
 


--- Page 6 ---

Hong Kong Legislation Extensible Markup Language (XML)  
Data Dictionary 
 
Version 1.0 
October 2019 
 
5 
Element Name 
Element Description 
Remark 
Use the @style attribute to specify appearance and dimensions. 
checkBox 
A <checkBox> is an inline tick box which denotes a box to be filled in on a form. 
 
Use the @style attribute to specify appearance and dimensions. 
 
b 
A <b> is a simple inline text to be shown in bold text. 
 
i 
An <i> is a simple inline text to be shown in italic text. 
 
sub 
A <sub> is a simple inline text to be shown in subscript text. 
 
sup 
A <sup> is a simple inline text to be shown in superscript text. 
 
del 
A <del> is a simple inline text to be shown in deleted text within a modification. 
 
ins 
An <ins> is a simple inline tag to be used to show inserted text within a modification.  
ordinance 
An <ordinance> contains the Ordinance. 
 
resolution 
A <resolution> is a document containing a resolution by the Legislative Council 
amending Ordinance or Subsidiary Legislation. 
 
subLeg 
A <subLeg> is a document containing Subsidiary Legislation. 
 
amendment 
An <amendment> is a document containing a committee stage amendment. 
 
docName 
The <docName> is a property containing the name of the document. 
 
docType 
The <docType> is a property that contains a short abbreviated string that describes 
the document type: 
             
cap - Ordinance or Subsidiary Legislation 
instrument - constitutional or other instrument 
 
docNumber 
The <docNumber> is a property that contains a numeric designation assigned to this 
document. 
 
 
docStatus 
The <docStatus> is a property used to record the current status of the legislation: 
 


--- Page 7 ---

Hong Kong Legislation Extensible Markup Language (XML)  
Data Dictionary 
 
Version 1.0 
October 2019 
 
6 
Element Name 
Element Description 
Remark 
Code 
Description 
in effect  
the legislation has been "enacted" and is in effect 
suspended  
the legislation has been "suspended" and is temporary 
not effective 
omitted  
the legislation has been "omitted" and is no longer in 
effect 
repealed  
the legislation has been "repealed" and is no longer in 
effect 
expired  
the legislation has been "expired" and is no longer in 
effect 
             
docTitle 
A <docTitle> is a statement that precedes the long title in an Ordinance or Subsidiary 
Legislation. 
 
longTitle 
A <longTitle> is a statement that sets out the purposes of the Bill or Ordinance. 
 
shortTitle 
The <shortTitle> is the short title defined in the first clause of the Bill or Ordinance. 
 
term 
A <term> is the defined term within a definition. 
 
preliminary 
A <preliminary> level consists of preliminary clauses that are outside of the main 
document hierarchy. 
 
section 
A <section> is the primary hierarchical level in an enacted legislation. 
 
The @role attribute defines variants of a section as found in Subsidiary Legislation. 
The following variants are predefined as values of the @role attribute: rule, regulation, 
bylaw, order, notice and direction 
 
subsection 
A <subsection> is a hierarchical level below a section. 
             
 


--- Page 8 ---

Hong Kong Legislation Extensible Markup Language (XML)  
Data Dictionary 
 
Version 1.0 
October 2019 
 
7 
Element Name 
Element Description 
Remark 
The @role attribute defines variants of a subsection as found in Subsidiary 
Legislation. The following variants are defined as a value of the @role attribute: rule, 
subrule and subregulation. 
part 
A <part> is the top hierarchical level for partitioning a Bill, Ordinance, or Subsidiary 
Legislation. 
 
division 
A <division> is the hierarchical level immediately below the Part level to further 
partition a Part level. 
 
subdivision 
A <subdivision> is a hierarchical level immediately below the Division level to further 
partition a Division level before the section level. 
 
chapter 
A <chapter> is a hierarchical level found above the section level in some legislations.  
article 
An <article> is the primary hierarchical level in some legislations. 
 
paragraph 
A <paragraph> is a level below a subsection in the document hierarchy. 
 
subparagraph 
A <subparagraph> is any level below a paragraph in the document hierarchy. 
 
For levels under subparagraph, use @role attribute values: 
Code 
Description 
subSubparagraph  
a level under subparagraph 
subSubSubparagraph  
a level under subsubparagraph 
subSubSubSubparapgraph  
a level under subsubsubparagraph 
           
 
item 
An <item> is a type of level which may not be numbered or non-designated. 
 
leadIn 
A <leadIn> is the introductory text that comes before lower levels in a level hierarchy.  
continued 
A <continued> is the interstitial text or the final text that comes after lower levels in 
a level hierarchy. 
 
proviso 
A <proviso> is a paragraph that states conditions relating to the law it is related to. 
 


--- Page 9 ---

Hong Kong Legislation Extensible Markup Language (XML)  
Data Dictionary 
 
Version 1.0 
October 2019 
 
8 
Element Name 
Element Description 
Remark 
amendingFormula 
An <amendingFormula> is an instruction used when defining an amendment to 
legislation or proposed legislation. 
 
commencementNote 
A <commencementNote> is a note included in an Ordinance or in Subsidiary 
Legislation to indicate when the Ordinance, or parts thereof, commenced operation. 
 
sourceNote 
A <sourceNote> is a note included to indicate the source of a provision. 
 
statutoryNote 
An <statutoryNote> is a note that is part of the law. 
 
editorialNote 
An <editorialNote> is a note included for editorial purposes only. While present in 
the text of the document as printed, it is not a part of the law. 
 
schedule 
A <schedule> is an appendix to a Bill, Ordinance, or Subsidiary Legislation which 
may contain a wide variety of content. 
 
annex 
An <annex> is another type of appendix to a Bill, Ordinance, or Subsidiary 
Legislation. 
 
 
2.2. Attribute 
Attribute Name 
Attribute Description 
Remark 
id 
The @id attribute should be assigned for an immutable (non-changing) value as an 
identifier of the element. 
xsd:ID datatype 
name 
The @name attribute is a local name that is intended to reflect the current identity 
of the element in a human-readable way. 
Maximum length: 256 
characters 
temporalId 
The @temporalId attribute is a name, scoped to the document, that is intended to 
reflect the current identity of the element in a human-readable way. 
Maximum length: 256 
characters 
identifier 
Use the @identifier attribute to specify the URL context of the element. Typically, 
the @identifier will be established on the root element or on any element, such as a 
<quotedStructure> or <quotedText> element, that changes the context. 
Maximum length: 256 
characters 


--- Page 10 ---

Hong Kong Legislation Extensible Markup Language (XML)  
Data Dictionary 
 
Version 1.0 
October 2019 
 
9 
Attribute Name 
Attribute Description 
Remark 
 
The @identifier attribute is optional. 
role 
Use the @role attribute to provide further refinement to an element's type. 
Maximum length: 32 
characters 
class 
The @class attribute can be used to specify presentation characteristics of an 
element that are not specified by the element name and the @role attribute. 
Maximum length: 256 
characters 
style 
The @style attribute is used to specify a special display style for an element. 
Maximum length: 1024 
characters 
note 
The @note attribute should be the primary mechanism for recording simple text 
notes to be associated with elements. 
Maximum length: 1024 
characters 
alt 
The @alt attribute is used to provide an alternative description of the element for the 
sake of web content accessibility. 
 
meta 
The @meta attribute should be used to associate metadata information with the 
element for search and other use. 
Maximum length: 1024 
characters 
title 
The @title attribute is used to specify the text describing the element in a table of 
contents or index. 
Maximum length: 1024 
characters 
brief 
The @brief attribute is an alternate method for providing a longer description of an 
element. 
 
Use the @brief attribute to provide a longer title or a title that is not to be used in 
the table of contents or an index. 
Maximum length: 1024 
characters 
sortOrder 
The @sortOrder attribute is used to specify a sorting order for a list of items, when 
that sort order is not the document sequence. 
Integer 
href 
The @href attribute is used to specify references to external documents or items in 
documents. 
xsd:anyURI datatype 


--- Page 11 ---

Hong Kong Legislation Extensible Markup Language (XML)  
Data Dictionary 
 
Version 1.0 
October 2019 
 
10 
Attribute Name 
Attribute Description 
Remark 
idref 
The @idref attribute is used to specify references to internal elements within the 
same document. 
xsd:IDREF datatype 
src 
The @src attribute is a URL of linking. 
xsd:anyURI datatype 
value 
The @value attribute is used when there is a single value. 
Maximum length: 256 
characters 
startValue 
The @startValue attribute is used for the lower end of a value range. 
Maximum length: 256 
characters 
endValue 
The @endValue attribute is used for the upper end of a value range. 
Maximum length: 256 
characters 
type 
When under NoteGroup, set the @type attribute to "footnote" to indicate that the 
notes contained should be shown in the footnotes at the end of the page or to 
"endnote" to indicate the notes contained should be shown at the end of the 
document. If not specified, "inline" is assumed.  
 
Possible value follows NoteTypeEnum: 
-inline 
-footnote 
-endnote 
 
When under ActionGroup, possible value follows ActionTypeEnum: 
- enact: The "enact" action enacts a law. 
- add: The "add" action adds text or provisions to existing law. 
- amend: The "amend" action modifies text or provisions in the existing law. 
- substitute: The "substitute" action replaces text or provisions in the existing law. 
- renumber: The "renumber" action changes the number of an existing provision in 
the existing law. 
- repeal: The "repeal" action repeals text or provisions in the existing law. 
- insert: The "insert" action adds text or provisions to a proposed law. 
- delete: The "delete" action removes text or provisions from a proposed law. 
 


--- Page 12 ---

Hong Kong Legislation Extensible Markup Language (XML)  
Data Dictionary 
 
Version 1.0 
October 2019 
 
11 
Attribute Name 
Attribute Description 
Remark 
 
When under PropertyType, possible value follows PropertyTypeEnum: 
-string 
-number 
-token 
-boolean 
-text 
-date 
-url 
 
When under SetType, possible value follows SetTypeEnum: 
-bag: A "bag" is an unordered but homogeneous collection of properties or sets. 
-seq: A "seq" (sequence) is an ordered and homogeneous sequence of properties or 
sets. 
-grp: A "grp" (group) is a heterogeneous collection of properties or sets. 
-alt: An "alt" (alternatives) is a homogeneous collection of properties or sets of 
which one is selected at any one time. 
-res: A "res" is a resource, such as a person, place, or thing and the properties 
enclosed within the set describe it. 
topic 
Set the @topic attribute to a string value in order to categorize the note or group of 
notes. An open, but enumerated, list of string values should be used. Using a fixed 
list of values will better aid in categorization of notes later. 
Maximum length: 256 
characters 
date 
The @date attribute is used for a single date value. 
Date/datetime 
startDate 
The @startDate attribute is used for the starting date of a date range. 
Date/datetime 
endDate 
The @endDate attribute is used for the ending date of a date range. 
Date/datetime 
status 
The @status attribute is used to show the status of a version of provision. 
Possible value follows 
StatusEnum: 
- pending 


--- Page 13 ---

Hong Kong Legislation Extensible Markup Language (XML)  
Data Dictionary 
 
Version 1.0 
October 2019 
 
12 
Attribute Name 
Attribute Description 
Remark 
- operational: Use the 
@partial attribute set to 
"true" for a partially 
operational (or partially 
commenced) status. 
- suspended: Use the 
@partial attribute set to 
"true" for a partially 
suspended status. 
- omitted 
- repealed 
- expiredSpent 
- other 
reason 
The @reason attribute is a modifier for the @status attribute and provides additional 
information about the status. Values for the @reason can be selected from the 
predefined ReasonEnum or by adding additional named tokens. 
Possible value: 
-notYetInOperation 
-inEffect 
-partiallyInOperation 
-partiallySuspended 
-suspended 
-omitted 
-omittedAsExpired 
-omittedAsSpent 
-omittedAsWithdrawn 
-repealed 
-repealedButRetained 
-cancelled 
-revoked 
-rescinded 
-expired 
-ceasedToBeInForce 


--- Page 14 ---

Hong Kong Legislation Extensible Markup Language (XML)  
Data Dictionary 
 
Version 1.0 
October 2019 
 
13 
Attribute Name 
Attribute Description 
Remark 
-ceasedToHaveEffect 
-ceasedToApply 
-hadItsEffect 
-spent 
-replaced 
-other 
-notAdopted 
-amendmentsIncorporated 
-rearranged 
partial 
The @partial attribute is used, in conjunction with the @status attribute to indicate 
that the status is not fully applied. 
Boolean 
occurrence 
Possible value: 
-(positive value) 
-“all” 
-“none” 
-“first” 
-“last” 
 
colspan 
The @colspan attribute, like the corresponding HTML attribute, defines the number 
of columns a column tag must span. If this attribute is not set, then the column span 
is one. 
Integer 
rowspan 
The @rowspan attribute, like the corresponding HTML attribute, defines the number 
of rows a specific row tag must span. If this attribute is not set, then the row span is 
one. 
Integer 
leaders 
The @leaders attribute specifies whether leaders should be shown either trailing or 
following the text content. 
Maximum length: 256 
characters 
orientation 
Possible value follows OrientationEnum: 
-portrait 
-landscape 
 


--- Page 15 ---

Hong Kong Legislation Extensible Markup Language (XML)  
Data Dictionary 
 
Version 1.0 
October 2019 
 
14 
Attribute Name 
Attribute Description 
Remark 
-landscapeLarge 
origin 
- QuotedTextType, QuotedStructureType 
The @origin attribute is used to refer to the origin of quoted text. The value must 
always be specified as a relative URL conforming to the reference specification. 
 
The @origin attribute is optional. 
xsd:anyURI datatype 
arrangement 
- LayoutType 
The @arrange attribute specifies how best to display the <layout>. 
 
- ImgType 
The @arrange attribute is used to guide the publishing engine how best to display 
the layout. By default, the layout will be published inline. 
Possible value follows 
ArrangementEnum: 
-leftSide 
-rightSide 
-inline 
-fullPage 
-foldout 
width 
The @width attribute is used to specify the width of an image. It is equivalent to the 
CSS "width" attribute. 
Integer 
height 
The @height attribute is used to specify the height of an image. It is equivalent to 
the CSS "height" attribute. 
Integer 
 
 
 
 
 
