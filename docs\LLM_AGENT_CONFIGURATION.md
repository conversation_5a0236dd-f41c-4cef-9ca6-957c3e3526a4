# Hong Kong Legal RAG System - LLM Agent Configuration Guide

## Overview

This guide provides comprehensive documentation for configuring LLM agents in the Hong Kong Legal RAG System, including trigger conditions, decision trees, agent behaviors, and confidence scoring mechanisms.

## Table of Contents

1. [Agent Architecture](#agent-architecture)
2. [Trigger Conditions and Decision Trees](#trigger-conditions-and-decision-trees)
3. [Agent Behavior Configuration](#agent-behavior-configuration)
4. [Confidence Scoring System](#confidence-scoring-system)
5. [Escalation Mechanisms](#escalation-mechanisms)
6. [Agent Personalities](#agent-personalities)
7. [Workflow Examples](#workflow-examples)

## Agent Architecture

### Core Agent Components

```python
class LegalAgent:
    def __init__(self, agent_type: AgentType, config: AgentConfig):
        self.agent_type = agent_type
        self.config = config
        self.confidence_threshold = config.confidence_threshold
        self.escalation_triggers = config.escalation_triggers
        self.personality = config.personality
        self.domain_expertise = config.domain_expertise
```

### Agent Types

1. **Primary Legal Agent**: Main legal consultation agent
2. **Specialist Agents**: Domain-specific agents (employment, criminal, commercial)
3. **Document Agent**: Legal document generation and review
4. **Citation Agent**: Legal citation verification and research
5. **Escalation Agent**: Human lawyer referral management

## Trigger Conditions and Decision Trees

### 1. Query Classification Triggers

```yaml
query_classification:
  employment_triggers:
    keywords: ["employment", "employee", "employer", "salary", "termination", "dismissal"]
    patterns: ["Employment Ordinance", "Cap. 57", "labour tribunal"]
    confidence_threshold: 0.8
    
  criminal_triggers:
    keywords: ["criminal", "arrest", "police", "court", "charge", "offense"]
    patterns: ["Crimes Ordinance", "Cap. 200", "criminal procedure"]
    confidence_threshold: 0.9  # Higher threshold for criminal matters
    
  commercial_triggers:
    keywords: ["company", "business", "commercial", "contract", "merger"]
    patterns: ["Companies Ordinance", "Cap. 622", "corporate"]
    confidence_threshold: 0.75
```

### 2. Urgency Assessment Decision Tree

```
Query Input
├── Contains urgent keywords? ("urgent", "immediate", "emergency")
│   ├── Yes → High Priority (Response time: <5 minutes)
│   └── No → Continue assessment
├── Legal deadline mentioned?
│   ├── Yes → Check deadline proximity
│   │   ├── <24 hours → High Priority
│   │   ├── <7 days → Medium Priority
│   │   └── >7 days → Normal Priority
│   └── No → Continue assessment
├── Criminal matter?
│   ├── Yes → High Priority (immediate legal representation needed)
│   └── No → Normal Priority
└── Default → Normal Priority (Response time: <30 minutes)
```

### 3. Complexity Assessment Triggers

```python
def assess_query_complexity(query: str, context: Dict) -> ComplexityLevel:
    complexity_indicators = {
        "high": [
            "multiple jurisdictions",
            "constitutional law",
            "class action",
            "regulatory investigation",
            "criminal charges",
            "judicial review"
        ],
        "medium": [
            "contract dispute",
            "employment termination",
            "corporate restructuring",
            "intellectual property"
        ],
        "low": [
            "simple definition",
            "basic procedure",
            "standard form",
            "general information"
        ]
    }
    
    # Scoring logic
    high_score = sum(1 for indicator in complexity_indicators["high"] 
                    if indicator.lower() in query.lower())
    medium_score = sum(1 for indicator in complexity_indicators["medium"] 
                      if indicator.lower() in query.lower())
    
    if high_score > 0:
        return ComplexityLevel.HIGH
    elif medium_score > 0:
        return ComplexityLevel.MEDIUM
    else:
        return ComplexityLevel.LOW
```

## Agent Behavior Configuration

### 1. Employment Law Agent Configuration

```yaml
employment_agent:
  name: "Hong Kong Employment Law Specialist"
  domain: "employment_law"
  
  behavior_settings:
    formality_level: "professional"
    explanation_depth: "detailed"
    citation_style: "comprehensive"
    practical_focus: true
    
  response_patterns:
    definition_requests:
      template: "employment_definition"
      include_chinese: true
      cite_ordinance: true
      
    procedure_questions:
      template: "employment_procedure"
      include_timeline: true
      mention_tribunal: true
      
    dispute_scenarios:
      template: "employment_dispute"
      suggest_mediation: true
      escalation_threshold: 0.7
      
  confidence_modifiers:
    employment_ordinance_questions: +0.2
    labour_tribunal_procedures: +0.15
    termination_issues: +0.1
    cross_border_employment: -0.3
```

### 2. Criminal Law Agent Configuration

```yaml
criminal_agent:
  name: "Hong Kong Criminal Law Specialist"
  domain: "criminal_law"
  
  behavior_settings:
    formality_level: "formal"
    urgency_awareness: "high"
    rights_emphasis: "mandatory"
    lawyer_referral: "immediate"
    
  mandatory_disclaimers:
    - "Seek immediate legal representation"
    - "Right to remain silent"
    - "Presumption of innocence"
    
  escalation_triggers:
    - "arrest"
    - "charge"
    - "court appearance"
    - "serious offense"
    
  confidence_modifiers:
    basic_procedure: +0.1
    serious_crimes: -0.4  # Lower confidence, immediate escalation
    bail_applications: -0.2
```

### 3. Commercial Law Agent Configuration

```yaml
commercial_agent:
  name: "Hong Kong Commercial Law Specialist"
  domain: "commercial_law"
  
  behavior_settings:
    business_focus: true
    risk_assessment: "comprehensive"
    compliance_emphasis: true
    cost_awareness: true
    
  response_enhancements:
    include_business_implications: true
    suggest_compliance_steps: true
    mention_regulatory_requirements: true
    provide_timeline_estimates: true
    
  confidence_modifiers:
    companies_ordinance: +0.2
    securities_regulations: +0.15
    cross_border_transactions: -0.2
    regulatory_investigations: -0.3
```

## Confidence Scoring System

### 1. Base Confidence Calculation

```python
def calculate_base_confidence(query_match: float, source_quality: float, 
                            domain_expertise: float) -> float:
    """
    Calculate base confidence score.
    
    Args:
        query_match: How well the query matches agent expertise (0-1)
        source_quality: Quality of retrieved legal sources (0-1)
        domain_expertise: Agent's domain expertise level (0-1)
    
    Returns:
        Base confidence score (0-1)
    """
    weights = {
        'query_match': 0.4,
        'source_quality': 0.4,
        'domain_expertise': 0.2
    }
    
    base_confidence = (
        query_match * weights['query_match'] +
        source_quality * weights['source_quality'] +
        domain_expertise * weights['domain_expertise']
    )
    
    return min(1.0, max(0.0, base_confidence))
```

### 2. Confidence Modifiers

```python
class ConfidenceModifiers:
    """Confidence adjustment factors for different scenarios."""
    
    DOMAIN_MATCH_BONUS = 0.2      # Query matches agent's primary domain
    RECENT_LAW_BONUS = 0.1        # Sources include recent legal updates
    MULTIPLE_SOURCES_BONUS = 0.15  # Multiple corroborating sources
    
    CROSS_DOMAIN_PENALTY = -0.2   # Query spans multiple legal domains
    OUTDATED_LAW_PENALTY = -0.3   # Sources may be outdated
    AMBIGUOUS_QUERY_PENALTY = -0.1 # Query lacks clarity
    
    CRIMINAL_MATTER_PENALTY = -0.4 # Criminal matters require human lawyers
    URGENT_MATTER_PENALTY = -0.2   # Urgent matters need immediate attention
    COMPLEX_MATTER_PENALTY = -0.25 # Complex matters need expert review
```

### 3. Confidence Thresholds

```python
CONFIDENCE_THRESHOLDS = {
    "criminal_law": {
        "proceed": 0.9,      # Very high threshold for criminal matters
        "caution": 0.7,      # Proceed with strong disclaimers
        "escalate": 0.5      # Below this, escalate to human lawyer
    },
    "employment_law": {
        "proceed": 0.7,      # Standard threshold
        "caution": 0.5,      # Proceed with disclaimers
        "escalate": 0.3      # Escalate for complex employment issues
    },
    "commercial_law": {
        "proceed": 0.75,     # Higher threshold for business matters
        "caution": 0.6,      # Business risk considerations
        "escalate": 0.4      # Complex commercial transactions
    },
    "general": {
        "proceed": 0.6,      # General legal information
        "caution": 0.4,      # Basic legal guidance
        "escalate": 0.2      # Refer to appropriate specialist
    }
}
```

## Escalation Mechanisms

### 1. Automatic Escalation Triggers

```python
def check_escalation_triggers(query: str, context: Dict, 
                            confidence: float, agent_type: str) -> Tuple[bool, str]:
    """
    Check if query should be escalated to human lawyer.
    
    Returns:
        (should_escalate, escalation_reason)
    """
    
    # Immediate escalation triggers
    immediate_triggers = [
        "arrest", "charged", "court appearance", "lawsuit filed",
        "regulatory investigation", "criminal offense", "police interview"
    ]
    
    for trigger in immediate_triggers:
        if trigger.lower() in query.lower():
            return True, f"Immediate legal representation required: {trigger}"
    
    # Confidence-based escalation
    threshold = CONFIDENCE_THRESHOLDS[agent_type]["escalate"]
    if confidence < threshold:
        return True, f"Low confidence ({confidence:.2f}) below threshold ({threshold})"
    
    # Complexity-based escalation
    if context.get("complexity") == "high":
        return True, "High complexity matter requires expert review"
    
    # Urgency-based escalation
    if context.get("urgency") == "urgent" and confidence < 0.8:
        return True, "Urgent matter with insufficient confidence"
    
    return False, ""
```

### 2. Escalation Workflow

```
Query Received
├── Check immediate triggers
│   ├── Criminal matter? → Immediate escalation
│   ├── Court deadline? → Priority escalation
│   └── Regulatory investigation? → Immediate escalation
├── Assess confidence score
│   ├── Below escalation threshold? → Schedule escalation
│   ├── In caution range? → Provide response with disclaimers
│   └── Above proceed threshold? → Provide standard response
└── Monitor user satisfaction
    ├── User requests human lawyer? → Escalate
    ├── Follow-up questions indicate confusion? → Consider escalation
    └── Positive feedback? → Continue with agent
```

## Agent Personalities

### 1. Formal Legal Advisor (Criminal/Constitutional Law)

```yaml
formal_advisor:
  tone: "formal"
  language_style: "precise_legal"
  
  characteristics:
    - Uses formal legal terminology
    - Emphasizes rights and procedures
    - Includes mandatory disclaimers
    - Recommends immediate legal representation
    
  response_patterns:
    greeting: "I am a Hong Kong legal information system. For criminal matters, immediate legal representation is essential."
    disclaimer: "This information is for general guidance only. Criminal matters require qualified legal representation."
    conclusion: "Please consult with a qualified criminal lawyer immediately."
```

### 2. Business-Friendly Advisor (Commercial Law)

```yaml
business_advisor:
  tone: "professional_friendly"
  language_style: "business_oriented"
  
  characteristics:
    - Balances legal accuracy with business practicality
    - Includes cost and timeline considerations
    - Focuses on risk management
    - Provides actionable recommendations
    
  response_patterns:
    greeting: "I'll help you understand the legal aspects of your business matter."
    analysis: "From both legal and business perspectives..."
    recommendations: "I recommend the following steps to manage legal and commercial risks..."
```

### 3. Citizen-Friendly Advisor (General Inquiries)

```yaml
citizen_advisor:
  tone: "approachable"
  language_style: "plain_english"
  
  characteristics:
    - Uses simple, clear language
    - Explains legal concepts in everyday terms
    - Provides step-by-step guidance
    - Emphasizes when to seek professional help
    
  response_patterns:
    greeting: "I'm here to help you understand Hong Kong law in simple terms."
    explanation: "In plain English, this means..."
    guidance: "Here's what you should do next..."
```

## Workflow Examples

### 1. Employment Termination Query Workflow

```
1. Query Classification
   ├── Keywords detected: "termination", "severance", "notice period"
   ├── Domain: Employment Law
   └── Agent: Employment Law Specialist

2. Complexity Assessment
   ├── Factors: Contract terms, length of service, reason for termination
   ├── Complexity: Medium
   └── Confidence modifier: Standard

3. Response Generation
   ├── Template: Employment termination analysis
   ├── Include: Notice periods, severance calculations, Labour Tribunal options
   └── Confidence: 0.75 (proceed with standard response)

4. Quality Check
   ├── Citations verified: Employment Ordinance sections
   ├── Practical guidance included: Yes
   └── Escalation needed: No

5. Response Delivery
   ├── Main response with legal analysis
   ├── Follow-up questions suggested
   └── Option to escalate if needed
```

### 2. Criminal Charge Query Workflow

```
1. Immediate Trigger Detection
   ├── Keywords: "charged", "criminal offense"
   ├── Trigger: Criminal matter
   └── Action: Immediate escalation protocol

2. Emergency Response
   ├── Template: Criminal matter emergency response
   ├── Content: Rights information, immediate lawyer referral
   └── Confidence: N/A (escalation required)

3. Lawyer Referral
   ├── Urgency: Immediate
   ├── Specialization: Criminal law
   └── Contact information provided

4. Follow-up
   ├── Confirm lawyer contact made
   ├── Provide general rights information
   └── Monitor case status
```

### 3. Commercial Contract Review Workflow

```
1. Document Analysis
   ├── Document type: Commercial agreement
   ├── Complexity: High (multiple jurisdictions)
   └── Agent: Commercial Law Specialist

2. Risk Assessment
   ├── Legal risks identified: 5 major issues
   ├── Business impact: High
   └── Confidence: 0.65 (caution range)

3. Response with Disclaimers
   ├── Preliminary analysis provided
   ├── Strong recommendation for lawyer review
   └── Risk mitigation suggestions

4. Escalation Recommendation
   ├── Reason: High-value commercial transaction
   ├── Urgency: Standard
   └── Specialist type: Commercial lawyer with M&A experience
```

## Configuration Best Practices

1. **Regular Calibration**: Adjust confidence thresholds based on user feedback
2. **Domain Expertise Updates**: Update agent knowledge with new legal developments
3. **Escalation Monitoring**: Track escalation rates and user satisfaction
4. **Performance Metrics**: Monitor response accuracy and user engagement
5. **Continuous Learning**: Incorporate successful interaction patterns

## Conclusion

Effective LLM agent configuration requires careful balance between:
- **Accuracy and Accessibility**: Providing accurate legal information in understandable terms
- **Automation and Human Oversight**: Knowing when to escalate to human experts
- **Efficiency and Safety**: Fast responses while maintaining legal safety standards
- **Personalization and Consistency**: Adapting to user needs while maintaining professional standards
