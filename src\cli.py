"""Command-line interface for Hong Kong Legal RAG System."""

import json
import logging
from pathlib import Path
from typing import Optional

import click

from .config import settings
from .pipeline.processing_pipeline import LegalDocumentPipeline
from .rag.legal_rag_system import LegalRAGSystem

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@click.group()
@click.option('--debug', is_flag=True, help='Enable debug logging')
def cli(debug):
    """Hong Kong Legal RAG System CLI."""
    if debug:
        logging.getLogger().setLevel(logging.DEBUG)


@cli.command()
@click.argument('directory_path', type=click.Path(exists=True))
@click.option('--force', is_flag=True, help='Force reprocessing of all files')
@click.option('--output', type=click.Path(), help='Output file for processing report')
def process(directory_path, force, output):
    """Process legal documents from a directory."""
    click.echo(f"Processing documents from: {directory_path}")
    
    try:
        pipeline = LegalDocumentPipeline()
        
        # Process the directory
        stats = pipeline.process_directory(directory_path, force_reprocess=force)
        
        click.echo("\nProcessing completed!")
        click.echo(f"Files processed: {stats['files_processed']}")
        click.echo(f"Definitions indexed: {stats['definitions_indexed']}")
        click.echo(f"Sections indexed: {stats['sections_indexed']}")
        click.echo(f"Schedules indexed: {stats['schedules_indexed']}")
        click.echo(f"Relationships created: {stats['relationships_created']}")
        
        # Export report if requested
        if output:
            pipeline.export_processing_report(output)
            click.echo(f"Processing report exported to: {output}")
        
        pipeline.close()
        
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        raise click.Abort()


@cli.command()
@click.argument('file_path', type=click.Path(exists=True))
@click.option('--verbose', is_flag=True, help='Show detailed extraction results')
def parse(file_path, verbose):
    """Parse a single XML file and show results."""
    click.echo(f"Parsing file: {file_path}")
    
    try:
        from .parsers.comprehensive_parser import ComprehensiveHKLMParser
        
        parser = ComprehensiveHKLMParser()
        extracted_content = parser.parse_file(file_path)
        
        if not extracted_content:
            click.echo("No content extracted from file", err=True)
            return
        
        # Show summary
        click.echo(f"\nDocument: {extracted_content.document_metadata.doc_name}")
        click.echo(f"Type: {extracted_content.document_type.value}")
        click.echo(f"Title: {extracted_content.document_metadata.title}")
        click.echo(f"Definitions: {len(extracted_content.definitions)}")
        click.echo(f"Sections: {len(extracted_content.sections)}")
        click.echo(f"Schedules: {len(extracted_content.schedules)}")
        click.echo(f"Amendments: {len(extracted_content.amendments)}")
        click.echo(f"References: {len(extracted_content.references)}")
        
        if verbose:
            click.echo("\nDefinitions:")
            for i, definition in enumerate(extracted_content.definitions[:5]):  # Show first 5
                click.echo(f"  {i+1}. {definition.name}")
                if definition.terms:
                    terms = ", ".join([term.text for term in definition.terms[:3]])
                    click.echo(f"     Terms: {terms}")
            
            click.echo("\nSections:")
            for i, section in enumerate(extracted_content.sections[:5]):  # Show first 5
                click.echo(f"  {i+1}. {section.section_id}: {section.heading}")
        
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        raise click.Abort()


@cli.command()
@click.argument('query')
@click.option('--limit', default=5, help='Number of results to return')
@click.option('--type', 'content_type', help='Filter by content type (definition, section, etc.)')
def search(query, limit, content_type):
    """Search legal documents."""
    click.echo(f"Searching for: {query}")
    
    try:
        pipeline = LegalDocumentPipeline()
        
        filters = {}
        if content_type:
            filters['element_type'] = content_type
        
        results = pipeline.search_documents(query, limit=limit, filters=filters)
        
        if not results:
            click.echo("No results found.")
            return
        
        click.echo(f"\nFound {len(results)} results:")
        for i, result in enumerate(results, 1):
            click.echo(f"\n{i}. {result['document']}")
            if result['section']:
                click.echo(f"   Section: {result['section']}")
            click.echo(f"   Type: {result['element_type']}")
            click.echo(f"   Score: {result['score']:.3f}")
            click.echo(f"   Content: {result['content'][:200]}...")
        
        pipeline.close()
        
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        raise click.Abort()


@cli.command()
def chat():
    """Start interactive chat with the legal AI assistant."""
    click.echo("Hong Kong Legal AI Assistant")
    click.echo("Type 'quit' to exit, 'help' for commands\n")
    
    try:
        pipeline = LegalDocumentPipeline()
        rag_system = LegalRAGSystem(pipeline)
        
        conversation_history = []
        
        while True:
            try:
                user_input = click.prompt("You", type=str)
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                elif user_input.lower() == 'help':
                    click.echo("\nCommands:")
                    click.echo("  quit/exit/q - Exit the chat")
                    click.echo("  help - Show this help")
                    click.echo("  clear - Clear conversation history")
                    continue
                elif user_input.lower() == 'clear':
                    conversation_history.clear()
                    click.echo("Conversation history cleared.")
                    continue
                
                # Get response from RAG system
                from .models import ChatMessage
                response = rag_system.chat(user_input, conversation_history)
                
                # Display response
                click.echo(f"\nAssistant: {response.message}")
                
                if response.sources:
                    click.echo(f"\nSources ({len(response.sources)}):")
                    for i, source in enumerate(response.sources[:3], 1):
                        click.echo(f"  {i}. {source.document} - {source.element_type.value}")
                
                if response.confidence < 0.5:
                    click.echo("\n⚠️  Low confidence response. Consider consulting a qualified lawyer.")
                
                if response.follow_up_questions:
                    click.echo("\nSuggested follow-up questions:")
                    for i, question in enumerate(response.follow_up_questions, 1):
                        click.echo(f"  {i}. {question}")
                
                # Add to conversation history
                conversation_history.append(ChatMessage(role="user", content=user_input))
                conversation_history.append(ChatMessage(role="assistant", content=response.message))
                
                # Keep only last 10 messages
                if len(conversation_history) > 10:
                    conversation_history = conversation_history[-10:]
                
                click.echo()
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                click.echo(f"Error: {e}", err=True)
        
        click.echo("\nGoodbye!")
        pipeline.close()
        
    except Exception as e:
        click.echo(f"Error initializing chat: {e}", err=True)
        raise click.Abort()


@cli.command()
def stats():
    """Show system statistics."""
    try:
        pipeline = LegalDocumentPipeline()
        stats = pipeline.get_processing_stats()
        
        click.echo("System Statistics:")
        click.echo("=" * 50)
        
        # Processing stats
        proc_stats = stats.get('processing_stats', {})
        click.echo(f"Total processed: {proc_stats.get('total_processed', 0)}")
        click.echo(f"Successful: {proc_stats.get('successful', 0)}")
        click.echo(f"Failed: {proc_stats.get('failed', 0)}")
        click.echo(f"Skipped: {proc_stats.get('skipped', 0)}")
        
        # Vector database stats
        vector_stats = stats.get('vector_stats', {})
        click.echo(f"\nVector Database:")
        for collection, info in vector_stats.items():
            click.echo(f"  {collection}: {info.get('count', 0)} items")
        
        # Graph database stats
        graph_stats = stats.get('graph_stats', {})
        if graph_stats:
            click.echo(f"\nGraph Database:")
            for stat_name, count in graph_stats.items():
                if stat_name != 'error':
                    click.echo(f"  {stat_name}: {count}")
        
        pipeline.close()
        
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        raise click.Abort()


@cli.command()
@click.option('--confirm', is_flag=True, help='Confirm deletion without prompt')
def clear(confirm):
    """Clear all processed data."""
    if not confirm:
        if not click.confirm("This will delete all processed data. Are you sure?"):
            click.echo("Operation cancelled.")
            return
    
    try:
        pipeline = LegalDocumentPipeline()
        pipeline.clear_all_data()
        click.echo("All data cleared successfully.")
        pipeline.close()
        
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        raise click.Abort()


@cli.command()
@click.option('--output', default='processing_report.json', help='Output file path')
def export(output):
    """Export processing report."""
    try:
        pipeline = LegalDocumentPipeline()
        pipeline.export_processing_report(output)
        click.echo(f"Processing report exported to: {output}")
        pipeline.close()
        
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        raise click.Abort()


if __name__ == '__main__':
    cli()
