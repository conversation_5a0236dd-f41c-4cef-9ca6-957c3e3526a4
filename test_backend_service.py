"""Comprehensive test suite for Hong Kong Legal RAG Backend Service."""

import asyncio
import json
import logging
import sys
from pathlib import Path

import pytest
from fastapi.testclient import TestClient

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.backend.api import app
from src.backend.auth import UserRole, UserPermission
from src.backend.legal_service import LegalIssueCategory, LegalDocumentType
from src.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test client
client = TestClient(app)


class TestBackendService:
    """Test suite for backend service functionality."""
    
    def __init__(self):
        self.access_token = None
        self.test_user_credentials = {
            "username": "demo_lawyer",
            "password": "demo_password"
        }
    
    def authenticate(self):
        """Authenticate and get access token."""
        response = client.post("/api/auth/login", json=self.test_user_credentials)
        
        if response.status_code == 200:
            data = response.json()
            self.access_token = data["access_token"]
            logger.info("Authentication successful")
            return True
        else:
            logger.error(f"Authentication failed: {response.status_code}")
            return False
    
    def get_auth_headers(self):
        """Get authorization headers."""
        if not self.access_token:
            if not self.authenticate():
                raise Exception("Failed to authenticate")
        
        return {"Authorization": f"Bearer {self.access_token}"}
    
    def test_health_check(self):
        """Test health check endpoint."""
        logger.info("Testing health check endpoint...")
        
        response = client.get("/health")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "status" in data
        assert "timestamp" in data
        assert "services" in data
        assert "metrics" in data
        
        logger.info(f"Health status: {data['status']}")
        logger.info(f"Services: {data['services']}")
        
        return True
    
    def test_authentication(self):
        """Test authentication system."""
        logger.info("Testing authentication system...")
        
        # Test login
        response = client.post("/api/auth/login", json=self.test_user_credentials)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "access_token" in data
        assert "refresh_token" in data
        assert "user_info" in data
        
        user_info = data["user_info"]
        assert user_info["role"] == "lawyer"
        assert "permissions" in user_info
        
        logger.info("Authentication test passed")
        return True
    
    def test_legal_query_processing(self):
        """Test legal query processing."""
        logger.info("Testing legal query processing...")
        
        headers = self.get_auth_headers()
        
        # Test employment law query
        employment_query = {
            "query": "What is the notice period for employment termination in Hong Kong?",
            "category": "employment",
            "language": "en",
            "urgency": "normal"
        }
        
        response = client.post("/api/legal/query", json=employment_query, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "query_id" in data
        assert "response" in data
        assert "category" in data
        assert "confidence" in data
        assert "sources" in data
        assert "legal_citations" in data
        
        logger.info(f"Query processed successfully. Confidence: {data['confidence']}")
        logger.info(f"Response length: {len(data['response'])} characters")
        
        return True
    
    def test_case_analysis(self):
        """Test legal case analysis."""
        logger.info("Testing legal case analysis...")
        
        headers = self.get_auth_headers()
        
        case_request = {
            "case_description": "Employee claims unfair dismissal after 5 years of service. Employer cites poor performance.",
            "category": "employment",
            "urgency": "normal",
            "client_type": "individual",
            "jurisdiction": "Hong Kong",
            "language": "en"
        }
        
        response = client.post("/api/legal/case-analysis", json=case_request, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "case_id" in data
        assert "analysis" in data
        assert "recommended_actions" in data
        assert "legal_precedents" in data
        assert "escalation_required" in data
        
        logger.info(f"Case analysis completed. Case ID: {data['case_id']}")
        logger.info(f"Escalation required: {data['escalation_required']}")
        
        return True
    
    def test_document_generation(self):
        """Test legal document generation."""
        logger.info("Testing legal document generation...")
        
        headers = self.get_auth_headers()
        
        document_request = {
            "document_type": "contract",
            "parameters": {
                "contract_type": "employment",
                "parties": [
                    {"name": "ABC Company Limited", "role": "employer"},
                    {"name": "John Doe", "role": "employee"}
                ],
                "position": "Software Developer",
                "salary": "HK$50,000"
            },
            "language": "en",
            "jurisdiction": "Hong Kong",
            "include_standard_clauses": True
        }
        
        response = client.post("/api/legal/document/generate", json=document_request, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "document_id" in data
        assert "document_type" in data
        assert "content" in data
        assert "compliance_check" in data
        assert "legal_review_required" in data
        
        logger.info(f"Document generated. ID: {data['document_id']}")
        logger.info(f"Legal review required: {data['legal_review_required']}")
        
        return True
    
    def test_citation_verification(self):
        """Test citation verification."""
        logger.info("Testing citation verification...")
        
        headers = self.get_auth_headers()
        
        citation = "Employment Ordinance (Cap. 57), s. 6"
        
        response = client.post(
            "/api/legal/citation/verify",
            params={"citation": citation},
            headers=headers
        )
        
        assert response.status_code == 200
        data = response.json()
        
        assert "citation" in data
        assert "is_valid" in data
        assert "verification_date" in data
        
        logger.info(f"Citation verification: {data['is_valid']}")
        
        return True
    
    def test_search_functionality(self):
        """Test document search functionality."""
        logger.info("Testing document search...")
        
        headers = self.get_auth_headers()
        
        search_params = {
            "query": "definition of adult",
            "limit": 5
        }
        
        response = client.post("/api/search", params=search_params, headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert "query" in data
        assert "results" in data
        assert "total_found" in data
        
        logger.info(f"Search completed. Found {data['total_found']} results")
        
        return True
    
    def test_system_statistics(self):
        """Test system statistics endpoint."""
        logger.info("Testing system statistics...")
        
        headers = self.get_auth_headers()
        
        response = client.get("/api/admin/stats", headers=headers)
        
        # Note: This might fail if user doesn't have admin permissions
        if response.status_code == 200:
            data = response.json()
            logger.info(f"System statistics retrieved: {list(data.keys())}")
        elif response.status_code == 403:
            logger.info("Admin statistics require higher permissions (expected)")
        else:
            logger.warning(f"Unexpected response: {response.status_code}")
        
        return True
    
    def test_legal_categories(self):
        """Test legal categories endpoint."""
        logger.info("Testing legal categories...")
        
        response = client.get("/api/legal/categories")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "categories" in data
        categories = data["categories"]
        
        assert len(categories) > 0
        
        category_values = [cat["value"] for cat in categories]
        assert "employment" in category_values
        assert "criminal" in category_values
        assert "commercial" in category_values
        
        logger.info(f"Available categories: {category_values}")
        
        return True
    
    def test_document_types(self):
        """Test document types endpoint."""
        logger.info("Testing document types...")
        
        response = client.get("/api/legal/document-types")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "document_types" in data
        doc_types = data["document_types"]
        
        assert len(doc_types) > 0
        
        type_values = [dt["value"] for dt in doc_types]
        assert "contract" in type_values
        assert "legal_opinion" in type_values
        
        logger.info(f"Available document types: {type_values}")
        
        return True
    
    def test_rate_limiting(self):
        """Test rate limiting functionality."""
        logger.info("Testing rate limiting...")
        
        headers = self.get_auth_headers()
        
        # Make multiple rapid requests
        responses = []
        for i in range(5):
            response = client.get("/api/legal/categories", headers=headers)
            responses.append(response)
        
        # Check for rate limit headers
        last_response = responses[-1]
        if "X-RateLimit-Limit" in last_response.headers:
            logger.info(f"Rate limit: {last_response.headers['X-RateLimit-Limit']}")
            logger.info(f"Remaining: {last_response.headers.get('X-RateLimit-Remaining', 'N/A')}")
        
        # All requests should succeed under normal rate limits
        for response in responses:
            assert response.status_code == 200
        
        logger.info("Rate limiting test passed")
        return True


def run_comprehensive_backend_tests():
    """Run all backend service tests."""
    logger.info("Starting comprehensive backend service tests...")
    
    test_suite = TestBackendService()
    
    tests = [
        ("Health Check", test_suite.test_health_check),
        ("Authentication", test_suite.test_authentication),
        ("Legal Query Processing", test_suite.test_legal_query_processing),
        ("Case Analysis", test_suite.test_case_analysis),
        ("Document Generation", test_suite.test_document_generation),
        ("Citation Verification", test_suite.test_citation_verification),
        ("Search Functionality", test_suite.test_search_functionality),
        ("System Statistics", test_suite.test_system_statistics),
        ("Legal Categories", test_suite.test_legal_categories),
        ("Document Types", test_suite.test_document_types),
        ("Rate Limiting", test_suite.test_rate_limiting),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running test: {test_name}")
        logger.info('='*50)
        
        try:
            result = test_func()
            results[test_name] = result
            status = "PASS" if result else "FAIL"
            logger.info(f"Test {test_name}: {status}")
        except Exception as e:
            logger.error(f"Test {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("BACKEND SERVICE TEST SUMMARY")
    logger.info('='*50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All backend service tests passed!")
        return True
    else:
        logger.warning("⚠️  Some backend service tests failed.")
        return False


def main():
    """Main test function."""
    print("Hong Kong Legal XML RAG System - Backend Service Tests")
    print("="*60)
    
    # Check if backend service is running
    try:
        response = client.get("/health")
        if response.status_code != 200:
            logger.error("Backend service is not responding properly")
            return 1
    except Exception as e:
        logger.error(f"Cannot connect to backend service: {e}")
        logger.info("Please start the backend service first:")
        logger.info("  python -m src.backend.api")
        return 1
    
    # Run tests
    success = run_comprehensive_backend_tests()
    
    if success:
        logger.info("\n🚀 Backend service is ready for production!")
        logger.info("  API Documentation: http://localhost:8000/api/docs")
        logger.info("  Health Check: http://localhost:8000/health")
        return 0
    else:
        logger.error("\n❌ Backend service has issues. Please check the logs above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
