"""Data models for Hong Kong Legal XML RAG System."""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field


class DocumentType(str, Enum):
    """Document type enumeration."""
    ORDINANCE = "ordinance"
    SUB_LEG = "subLeg"
    RESOLUTION = "resolution"
    AMENDMENT = "amendment"


class ContentType(str, Enum):
    """Content type classification."""
    PRIMARY = "primary"
    SECONDARY = "secondary"
    AMENDMENT = "amendment"
    ANNOTATION = "annotation"
    STRUCTURAL = "structural"


class ElementType(str, Enum):
    """XML element type enumeration."""
    DEFINITION = "definition"
    SECTION = "section"
    SUBSECTION = "subsection"
    PARAGRAPH = "paragraph"
    HEADING = "heading"
    CONTENT = "content"
    TERM = "term"
    REFERENCE = "reference"
    SCHEDULE = "schedule"
    NOTE = "note"


class Language(str, Enum):
    """Language enumeration."""
    ENGLISH = "en"
    CHINESE = "zh-Hant-HK"


class DocumentStatus(str, Enum):
    """Document status enumeration."""
    IN_EFFECT = "In effect"
    SUSPENDED = "Suspended"
    REPEALED = "Repealed"
    PENDING = "Pending"
    EXPIRED = "Expired"


class ElementMetadata(BaseModel):
    """Metadata for XML elements."""
    id: Optional[str] = None
    name: Optional[str] = None
    temporal_id: Optional[str] = None
    start_period: Optional[str] = None
    end_period: Optional[str] = None
    status: Optional[str] = None
    reason: Optional[str] = None
    partial: Optional[bool] = None
    role: Optional[str] = None
    class_attr: Optional[str] = Field(None, alias="class")
    style: Optional[str] = None
    note: Optional[str] = None
    title: Optional[str] = None
    brief: Optional[str] = None


class HierarchyLevel(BaseModel):
    """Hierarchical level information."""
    element: str
    name: Optional[str] = None
    id: Optional[str] = None
    level: int


class Term(BaseModel):
    """Legal term with language support."""
    text: str
    language: Language = Language.ENGLISH
    definition: Optional[str] = None


class Reference(BaseModel):
    """Reference to other legal documents or sections."""
    href: Optional[str] = None
    idref: Optional[str] = None
    text: str
    type: str = "internal"  # internal, external, amendment


class Definition(BaseModel):
    """Legal definition structure."""
    name: str
    terms: List[Term] = []
    content: str
    lead_in: Optional[str] = None
    paragraphs: List[Dict[str, Any]] = []
    metadata: ElementMetadata
    hierarchy: List[HierarchyLevel] = []
    references: List[Reference] = []


class LegalSection(BaseModel):
    """Legal section structure."""
    section_id: str
    role: Optional[str] = None
    heading: Optional[str] = None
    content: str
    subsections: List[Dict[str, Any]] = []
    metadata: ElementMetadata
    hierarchy: List[HierarchyLevel] = []
    references: List[Reference] = []


class Schedule(BaseModel):
    """Schedule/appendix structure."""
    name: str
    heading: Optional[str] = None
    content: str
    layout: Optional[Dict[str, Any]] = None
    metadata: ElementMetadata
    hierarchy: List[HierarchyLevel] = []


class Amendment(BaseModel):
    """Amendment information."""
    source_note: str
    amending_document: Optional[str] = None
    amendment_type: str  # Added, Amended, Replaced, Omitted
    effective_date: Optional[datetime] = None
    references: List[Reference] = []


class DocumentMetadata(BaseModel):
    """Document-level metadata."""
    doc_name: Optional[str] = None
    doc_type: Optional[str] = None
    doc_number: Optional[str] = None
    doc_status: Optional[DocumentStatus] = None
    identifier: Optional[str] = None
    date: Optional[str] = None
    language: Optional[Language] = None
    publisher: Optional[str] = None
    title: Optional[str] = None
    short_title: Optional[str] = None


class ExtractedContent(BaseModel):
    """Complete extracted content from XML document."""
    document_metadata: DocumentMetadata
    document_type: DocumentType
    definitions: List[Definition] = []
    sections: List[LegalSection] = []
    schedules: List[Schedule] = []
    amendments: List[Amendment] = []
    references: List[Reference] = []
    hierarchy: List[HierarchyLevel] = []
    
    # Processing metadata
    file_path: str
    extracted_at: datetime = Field(default_factory=datetime.now)
    content_hash: Optional[str] = None


class SearchQuery(BaseModel):
    """Search query structure."""
    query: str
    filters: Dict[str, Any] = {}
    limit: int = 10
    include_definitions: bool = True
    include_sections: bool = True
    include_schedules: bool = True
    language: Optional[Language] = None


class SearchResult(BaseModel):
    """Search result structure."""
    content: str
    content_type: ContentType
    element_type: ElementType
    document: str
    section: Optional[str] = None
    score: float
    metadata: Dict[str, Any] = {}
    hierarchy: List[HierarchyLevel] = []
    references: List[Reference] = []


class ChatMessage(BaseModel):
    """Chat message structure."""
    role: str  # user, assistant, system
    content: str
    timestamp: datetime = Field(default_factory=datetime.now)
    metadata: Dict[str, Any] = {}


class ChatResponse(BaseModel):
    """Chat response structure."""
    message: str
    sources: List[SearchResult] = []
    confidence: float = 0.0
    reasoning: Optional[str] = None
    follow_up_questions: List[str] = []
