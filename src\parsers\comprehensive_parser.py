"""Comprehensive Hong Kong Legal XML Parser based on complete data dictionary analysis."""

import hashlib
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from bs4 import BeautifulSoup, Tag

from ..models import (
    Amendment, Definition, DocumentMetadata, DocumentType, ElementMetadata,
    ExtractedContent, HierarchyLevel, Language, LegalSection, Reference,
    Schedule, Term
)


class ComprehensiveHKLMParser:
    """Comprehensive parser implementing all 80+ XML elements from HKLM data dictionary."""
    
    def __init__(self):
        self.namespace = {
            'hklm': 'http://www.xml.gov.hk/schemas/hklm/1.0',
            'dc': 'http://purl.org/dc/elements/1.1/'
        }
        
        # Element classification for content prioritization
        self.primary_elements = {
            'content', 'def', 'term', 'heading', 'leadIn', 'continued', 
            'proviso', 'quotedText', 'quotedStructure'
        }
        
        self.secondary_elements = {
            'num', 'subheading', 'crossHeading', 'preamble', 'recital',
            'enactingFormula', 'longTitle', 'docTitle', 'shortTitle'
        }
        
        self.amendment_elements = {
            'sourceNote', 'commencementNote', 'amendingFormula', 
            'instruction', 'action'
        }
        
        self.structural_elements = {
            'schedule', 'annex', 'appendix', 'layout', 'header', 'row', 
            'column', 'toc', 'tocItem'
        }
    
    def parse_file(self, file_path: Union[str, Path]) -> ExtractedContent:
        """Parse a single XML file and return comprehensive structured content."""
        file_path = Path(file_path)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Generate content hash for change detection
        content_hash = hashlib.md5(content.encode()).hexdigest()
        
        extracted = self.parse_content(content)
        extracted.file_path = str(file_path)
        extracted.content_hash = content_hash
        
        return extracted
    
    def parse_content(self, xml_content: str) -> ExtractedContent:
        """Parse XML content string into comprehensive structured format."""
        soup = BeautifulSoup(xml_content, 'xml')
        
        # Determine document type
        root = soup.find(['ordinance', 'subLeg', 'resolution', 'amendment'])
        if not root:
            raise ValueError("Unknown document type - no recognized root element found")
        
        doc_type = DocumentType(root.name)
        
        # Extract all components comprehensively
        document_metadata = self._extract_document_metadata(root)
        definitions = self._extract_all_definitions(root)
        sections = self._extract_all_sections(root)
        schedules = self._extract_all_schedules(root)
        amendments = self._extract_all_amendments(root)
        references = self._extract_all_references(root)
        hierarchy = self._build_document_hierarchy(root)
        
        return ExtractedContent(
            document_metadata=document_metadata,
            document_type=doc_type,
            definitions=definitions,
            sections=sections,
            schedules=schedules,
            amendments=amendments,
            references=references,
            hierarchy=hierarchy,
            file_path="",  # Will be set by parse_file
            extracted_at=datetime.now()
        )
    
    def _extract_document_metadata(self, root: Tag) -> DocumentMetadata:
        """Extract comprehensive document metadata."""
        meta = root.find('meta')
        if not meta:
            return DocumentMetadata()
        
        # Extract basic document information
        doc_name = self._get_element_text(meta.find('docName'))
        doc_type = self._get_element_text(meta.find('docType'))
        doc_number = self._get_element_text(meta.find('docNumber'))
        doc_status = self._get_element_text(meta.find('docStatus'))
        
        # Extract Dublin Core metadata
        identifier = self._get_element_text(meta.find('dc:identifier'))
        date = self._get_element_text(meta.find('dc:date'))
        language = self._get_element_text(meta.find('dc:language'))
        publisher = self._get_element_text(meta.find('dc:publisher'))
        
        # Extract titles from main section
        main = root.find('main')
        title = None
        short_title = None
        
        if main:
            title_elem = main.find(['longTitle', 'docTitle'])
            title = self._get_element_text(title_elem) if title_elem else None
            
            short_title_elem = main.find('shortTitle')
            short_title = self._get_element_text(short_title_elem) if short_title_elem else None
        
        return DocumentMetadata(
            doc_name=doc_name,
            doc_type=doc_type,
            doc_number=doc_number,
            doc_status=doc_status,
            identifier=identifier,
            date=date,
            language=Language(language) if language in ['en', 'zh-Hant-HK'] else Language.ENGLISH,
            publisher=publisher,
            title=title,
            short_title=short_title
        )
    
    def _extract_all_definitions(self, root: Tag) -> List[Definition]:
        """Extract all types of definitions comprehensively."""
        definitions = []
        
        # Type 1: Explicit definitions with <def name="...">
        for def_elem in root.find_all('def'):
            if def_elem.get('name'):  # Only named definitions
                definition = self._parse_definition(def_elem)
                if definition:
                    definitions.append(definition)
        
        # Type 2: Ordinance-style definitions (sections with "Definition of" in heading)
        for section in root.find_all('section'):
            heading = section.find('heading')
            if heading and 'definition of' in heading.get_text().lower():
                definition = self._parse_ordinance_definition(section)
                if definition:
                    definitions.append(definition)
        
        return definitions
    
    def _parse_definition(self, def_elem: Tag) -> Optional[Definition]:
        """Parse explicit definition element with full metadata."""
        name = def_elem.get('name')
        if not name:
            return None
        
        terms = self._extract_terms(def_elem)
        content = self._extract_definition_content(def_elem)
        lead_in = self._extract_lead_in(def_elem)
        paragraphs = self._extract_paragraphs(def_elem)
        metadata = self._extract_element_metadata(def_elem)
        hierarchy = self._get_element_hierarchy(def_elem)
        references = self._extract_references_from_element(def_elem)
        
        return Definition(
            name=name,
            terms=terms,
            content=content,
            lead_in=lead_in,
            paragraphs=paragraphs,
            metadata=metadata,
            hierarchy=hierarchy,
            references=references
        )
    
    def _extract_terms(self, element: Tag) -> List[Term]:
        """Extract bilingual terms with language attributes."""
        terms = []
        for term_elem in element.find_all('term'):
            lang_attr = term_elem.get('xml:lang', 'en')
            language = Language.CHINESE if lang_attr == 'zh-Hant-HK' else Language.ENGLISH
            
            term = Term(
                text=term_elem.get_text().strip(),
                language=language
            )
            terms.append(term)
        return terms
    
    def _extract_definition_content(self, def_elem: Tag) -> str:
        """Extract definition content excluding terms and metadata."""
        # Clone element to avoid modifying original
        content_elem = def_elem.__copy__()
        
        # Remove term elements to get pure definition content
        for term in content_elem.find_all('term'):
            term.decompose()
        
        # Remove source notes and other metadata
        for note in content_elem.find_all(['sourceNote', 'editorialNote']):
            note.decompose()
        
        return content_elem.get_text().strip()
    
    def _extract_lead_in(self, element: Tag) -> Optional[str]:
        """Extract leadIn element content."""
        lead_in = element.find('leadIn')
        return lead_in.get_text().strip() if lead_in else None
    
    def _extract_paragraphs(self, element: Tag) -> List[Dict[str, Any]]:
        """Extract paragraph structure with full hierarchy."""
        paragraphs = []
        
        for para in element.find_all('paragraph'):
            para_data = {
                'id': para.get('name'),
                'num': self._get_element_text(para.find('num')),
                'content': self._get_element_text(para.find('content')),
                'role': para.get('role'),
                'metadata': self._extract_element_metadata(para),
                'subparagraphs': self._extract_subparagraphs(para)
            }
            paragraphs.append(para_data)
        
        return paragraphs
    
    def _extract_subparagraphs(self, element: Tag) -> List[Dict[str, Any]]:
        """Extract subparagraph structure."""
        subparagraphs = []
        
        for subpara in element.find_all('subparagraph'):
            subpara_data = {
                'id': subpara.get('name'),
                'num': self._get_element_text(subpara.find('num')),
                'content': self._get_element_text(subpara.find('content')),
                'role': subpara.get('role'),
                'metadata': self._extract_element_metadata(subpara)
            }
            subparagraphs.append(subpara_data)
        
        return subparagraphs
    
    def _extract_element_metadata(self, element: Tag) -> ElementMetadata:
        """Extract comprehensive metadata from any element."""
        return ElementMetadata(
            id=element.get('id'),
            name=element.get('name'),
            temporal_id=element.get('temporalId'),
            start_period=element.get('startPeriod'),
            end_period=element.get('endPeriod'),
            status=element.get('status'),
            reason=element.get('reason'),
            partial=element.get('partial') == 'true' if element.get('partial') else None,
            role=element.get('role'),
            class_attr=element.get('class'),
            style=element.get('style'),
            note=element.get('note'),
            title=element.get('title'),
            brief=element.get('brief')
        )
    
    def _get_element_hierarchy(self, element: Tag) -> List[HierarchyLevel]:
        """Get full hierarchical path from root to element."""
        hierarchy = []
        current = element
        level = 0
        
        while current and current.name:
            if current.get('name') or current.name in ['ordinance', 'subLeg', 'resolution']:
                hierarchy.insert(0, HierarchyLevel(
                    element=current.name,
                    name=current.get('name'),
                    id=current.get('id'),
                    level=level
                ))
                level += 1
            current = current.parent
        
        return hierarchy
    
    def _get_element_text(self, element: Optional[Tag]) -> str:
        """Safely get text from element."""
        return element.get_text().strip() if element else ""

    def _parse_ordinance_definition(self, section: Tag) -> Optional[Definition]:
        """Parse ordinance-style definition section."""
        section_id = section.get('name')
        if not section_id:
            return None

        heading = section.find('heading')
        heading_text = self._get_element_text(heading) if heading else ""

        # Extract term from heading
        terms = self._extract_terms_from_heading(heading_text)

        # Combine all content from subsections
        content_parts = []
        paragraphs = []

        for subsection in section.find_all('subsection'):
            subsection_content = self._get_element_text(subsection.find('content'))
            if subsection_content:
                content_parts.append(subsection_content)

            # Extract paragraphs from subsection
            subsection_paragraphs = self._extract_paragraphs(subsection)
            paragraphs.extend(subsection_paragraphs)

        content = ' '.join(content_parts)
        metadata = self._extract_element_metadata(section)
        hierarchy = self._get_element_hierarchy(section)
        references = self._extract_references_from_element(section)

        return Definition(
            name=section_id,
            terms=terms,
            content=content,
            lead_in=None,
            paragraphs=paragraphs,
            metadata=metadata,
            hierarchy=hierarchy,
            references=references
        )

    def _extract_terms_from_heading(self, heading_text: str) -> List[Term]:
        """Extract terms from heading text like 'Definition of bill of exchange'."""
        terms = []

        # Simple extraction - look for text after "definition of"
        if 'definition of' in heading_text.lower():
            term_text = heading_text.lower().split('definition of', 1)[1].strip()
            if term_text:
                terms.append(Term(text=term_text, language=Language.ENGLISH))

        return terms

    def _extract_all_sections(self, root: Tag) -> List[LegalSection]:
        """Extract all legal sections comprehensively."""
        sections = []

        for section in root.find_all('section'):
            # Skip sections that are definitions (already processed)
            heading = section.find('heading')
            if heading and 'definition of' in heading.get_text().lower():
                continue

            legal_section = self._parse_legal_section(section)
            if legal_section:
                sections.append(legal_section)

        return sections

    def _parse_legal_section(self, section: Tag) -> Optional[LegalSection]:
        """Parse a legal section element with full metadata."""
        section_id = section.get('name')
        if not section_id:
            return None

        role = section.get('role')
        heading = self._get_element_text(section.find('heading'))
        content = self._extract_section_content(section)
        subsections = self._extract_subsections(section)
        metadata = self._extract_element_metadata(section)
        hierarchy = self._get_element_hierarchy(section)
        references = self._extract_references_from_element(section)

        return LegalSection(
            section_id=section_id,
            role=role,
            heading=heading,
            content=content,
            subsections=subsections,
            metadata=metadata,
            hierarchy=hierarchy,
            references=references
        )

    def _extract_section_content(self, section: Tag) -> str:
        """Extract main content from section, excluding subsections."""
        content_parts = []

        # Direct content element
        content_elem = section.find('content', recursive=False)
        if content_elem:
            content_parts.append(content_elem.get_text().strip())

        # Lead-in text
        lead_in = section.find('leadIn', recursive=False)
        if lead_in:
            content_parts.append(lead_in.get_text().strip())

        # Continued text
        continued = section.find('continued', recursive=False)
        if continued:
            content_parts.append(continued.get_text().strip())

        # Proviso text
        proviso = section.find('proviso', recursive=False)
        if proviso:
            content_parts.append(proviso.get_text().strip())

        return ' '.join(content_parts)

    def _extract_subsections(self, section: Tag) -> List[Dict[str, Any]]:
        """Extract subsections with full structure."""
        subsections = []

        for subsection in section.find_all('subsection'):
            subsection_data = {
                'id': subsection.get('name'),
                'role': subsection.get('role'),
                'num': self._get_element_text(subsection.find('num')),
                'content': self._extract_section_content(subsection),
                'paragraphs': self._extract_paragraphs(subsection),
                'metadata': self._extract_element_metadata(subsection),
                'hierarchy': self._get_element_hierarchy(subsection)
            }
            subsections.append(subsection_data)

        return subsections

    def _extract_all_schedules(self, root: Tag) -> List[Schedule]:
        """Extract all schedules and appendices."""
        schedules = []

        for schedule_elem in root.find_all(['schedule', 'annex', 'appendix']):
            schedule = self._parse_schedule(schedule_elem)
            if schedule:
                schedules.append(schedule)

        return schedules

    def _parse_schedule(self, schedule_elem: Tag) -> Optional[Schedule]:
        """Parse schedule element with layout support."""
        name = schedule_elem.get('name')
        if not name:
            return None

        heading = self._get_element_text(schedule_elem.find('heading'))
        content = self._extract_schedule_content(schedule_elem)
        layout = self._extract_layout(schedule_elem)
        metadata = self._extract_element_metadata(schedule_elem)
        hierarchy = self._get_element_hierarchy(schedule_elem)

        return Schedule(
            name=name,
            heading=heading,
            content=content,
            layout=layout,
            metadata=metadata,
            hierarchy=hierarchy
        )

    def _extract_schedule_content(self, schedule_elem: Tag) -> str:
        """Extract content from schedule, excluding layout tables."""
        content_parts = []

        # Get all text content except from layout elements
        for elem in schedule_elem.find_all(text=True):
            parent = elem.parent
            if parent and parent.name not in ['layout', 'header', 'row', 'column']:
                text = elem.strip()
                if text:
                    content_parts.append(text)

        return ' '.join(content_parts)

    def _extract_layout(self, element: Tag) -> Optional[Dict[str, Any]]:
        """Extract tabular layout structure."""
        layout_elem = element.find('layout')
        if not layout_elem:
            return None

        layout_data = {
            'arrangement': layout_elem.get('arrangement'),
            'orientation': layout_elem.get('orientation'),
            'headers': [],
            'rows': []
        }

        # Extract headers
        for header in layout_elem.find_all('header'):
            header_data = {
                'columns': [col.get_text().strip() for col in header.find_all('column')]
            }
            layout_data['headers'].append(header_data)

        # Extract rows
        for row in layout_elem.find_all('row'):
            row_data = {
                'columns': [],
                'rowspan': row.get('rowspan'),
                'metadata': self._extract_element_metadata(row)
            }
            for col in row.find_all('column'):
                col_data = {
                    'content': col.get_text().strip(),
                    'colspan': col.get('colspan'),
                    'rowspan': col.get('rowspan')
                }
                row_data['columns'].append(col_data)
            layout_data['rows'].append(row_data)

        return layout_data

    def _extract_all_amendments(self, root: Tag) -> List[Amendment]:
        """Extract all amendment information."""
        amendments = []

        # Extract from source notes
        for source_note in root.find_all('sourceNote'):
            amendment = self._parse_source_note(source_note)
            if amendment:
                amendments.append(amendment)

        # Extract from amending formulas
        for amending_formula in root.find_all('amendingFormula'):
            amendment = self._parse_amending_formula(amending_formula)
            if amendment:
                amendments.append(amendment)

        return amendments

    def _parse_source_note(self, source_note: Tag) -> Optional[Amendment]:
        """Parse source note into amendment information."""
        text = source_note.get_text().strip()
        if not text:
            return None

        # Extract amendment type and document reference
        amendment_type = "Unknown"
        amending_document = None

        if "Added" in text:
            amendment_type = "Added"
        elif "Amended" in text:
            amendment_type = "Amended"
        elif "Replaced" in text:
            amendment_type = "Replaced"
        elif "Omitted" in text:
            amendment_type = "Omitted"

        # Extract references from the source note
        references = self._extract_references_from_element(source_note)
        if references:
            amending_document = references[0].text

        return Amendment(
            source_note=text,
            amending_document=amending_document,
            amendment_type=amendment_type,
            references=references
        )

    def _parse_amending_formula(self, amending_formula: Tag) -> Optional[Amendment]:
        """Parse amending formula into amendment information."""
        text = amending_formula.get_text().strip()
        if not text:
            return None

        references = self._extract_references_from_element(amending_formula)

        return Amendment(
            source_note=text,
            amendment_type="Amendment Formula",
            references=references
        )

    def _extract_all_references(self, root: Tag) -> List[Reference]:
        """Extract all references from the document."""
        references = []

        for ref_elem in root.find_all('ref'):
            reference = self._parse_reference(ref_elem)
            if reference:
                references.append(reference)

        return references

    def _parse_reference(self, ref_elem: Tag) -> Optional[Reference]:
        """Parse reference element."""
        text = ref_elem.get_text().strip()
        if not text:
            return None

        href = ref_elem.get('href')
        idref = ref_elem.get('idref')

        # Determine reference type
        ref_type = "internal"
        if href:
            if href.startswith('/hk/'):
                ref_type = "external"
            elif href.startswith('http'):
                ref_type = "external"

        return Reference(
            href=href,
            idref=idref,
            text=text,
            type=ref_type
        )

    def _extract_references_from_element(self, element: Tag) -> List[Reference]:
        """Extract all references from within an element."""
        references = []

        for ref_elem in element.find_all('ref'):
            reference = self._parse_reference(ref_elem)
            if reference:
                references.append(reference)

        return references

    def _build_document_hierarchy(self, root: Tag) -> List[HierarchyLevel]:
        """Build complete document hierarchy."""
        hierarchy = []

        # Document level
        hierarchy.append(HierarchyLevel(
            element=root.name,
            name=root.get('name'),
            id=root.get('id'),
            level=0
        ))

        # Main sections
        main = root.find('main')
        if main:
            hierarchy.append(HierarchyLevel(
                element='main',
                name=main.get('name'),
                id=main.get('id'),
                level=1
            ))

            # Parts
            for part in main.find_all('part'):
                hierarchy.append(HierarchyLevel(
                    element='part',
                    name=part.get('name'),
                    id=part.get('id'),
                    level=2
                ))

        return hierarchy
