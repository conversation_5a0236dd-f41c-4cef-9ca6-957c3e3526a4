"""Comprehensive system test for Hong Kong Legal XML RAG System."""

import logging
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config import settings
from src.parsers.comprehensive_parser import ComprehensiveHKLMParser
from src.vectorstore.comprehensive_vector_manager import ComprehensiveVectorManager
from src.graph.relationship_manager import LegalRelationshipManager
from src.pipeline.processing_pipeline import LegalDocumentPipeline
from src.rag.legal_rag_system import LegalRAGSystem

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_xml_parsing():
    """Test XML parsing functionality."""
    logger.info("Testing XML parsing...")
    
    parser = ComprehensiveHKLMParser()
    xml_dir = Path(settings.xml_dir)
    
    if not xml_dir.exists():
        logger.warning(f"XML directory not found: {xml_dir}")
        return False
    
    xml_files = list(xml_dir.rglob("*.xml"))
    if not xml_files:
        logger.warning("No XML files found")
        return False
    
    # Test parsing first file
    test_file = xml_files[0]
    logger.info(f"Testing parsing of: {test_file}")
    
    try:
        extracted_content = parser.parse_file(test_file)
        
        if not extracted_content:
            logger.error("Failed to extract content")
            return False
        
        logger.info(f"Successfully parsed: {extracted_content.document_metadata.doc_name}")
        logger.info(f"Document type: {extracted_content.document_type.value}")
        logger.info(f"Definitions: {len(extracted_content.definitions)}")
        logger.info(f"Sections: {len(extracted_content.sections)}")
        logger.info(f"Schedules: {len(extracted_content.schedules)}")
        logger.info(f"References: {len(extracted_content.references)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Parsing failed: {e}")
        return False


def test_vector_indexing():
    """Test vector database indexing."""
    logger.info("Testing vector indexing...")
    
    try:
        vector_manager = ComprehensiveVectorManager()
        stats = vector_manager.get_collection_stats()
        
        logger.info("Vector database collections:")
        for collection, info in stats.items():
            logger.info(f"  {collection}: {info.get('count', 0)} items")
        
        return True
        
    except Exception as e:
        logger.error(f"Vector indexing test failed: {e}")
        return False


def test_graph_database():
    """Test graph database connectivity."""
    logger.info("Testing graph database...")
    
    try:
        relationship_manager = LegalRelationshipManager()
        
        if not relationship_manager.driver:
            logger.warning("Graph database not available (this is optional)")
            return True
        
        stats = relationship_manager.get_document_statistics()
        logger.info("Graph database statistics:")
        for stat_name, count in stats.items():
            if stat_name != 'error':
                logger.info(f"  {stat_name}: {count}")
        
        relationship_manager.close()
        return True
        
    except Exception as e:
        logger.error(f"Graph database test failed: {e}")
        return False


def test_pipeline():
    """Test the complete processing pipeline."""
    logger.info("Testing processing pipeline...")
    
    try:
        pipeline = LegalDocumentPipeline()
        stats = pipeline.get_processing_stats()
        
        logger.info("Pipeline statistics:")
        logger.info(f"  Processed documents: {stats.get('processed_documents', 0)}")
        
        # Test search functionality
        search_results = pipeline.search_documents("definition", limit=3)
        logger.info(f"  Search test returned {len(search_results)} results")
        
        pipeline.close()
        return True
        
    except Exception as e:
        logger.error(f"Pipeline test failed: {e}")
        return False


def test_rag_system():
    """Test the RAG system."""
    logger.info("Testing RAG system...")
    
    try:
        pipeline = LegalDocumentPipeline()
        rag_system = LegalRAGSystem(pipeline)
        
        if not rag_system.llm_client:
            logger.warning("LLM client not available - configure API keys for full functionality")
            pipeline.close()
            return True
        
        # Test chat functionality
        test_query = "What is the definition of adult?"
        logger.info(f"Testing query: {test_query}")
        
        response = rag_system.chat(test_query)
        
        logger.info(f"Response received: {len(response.message)} characters")
        logger.info(f"Sources: {len(response.sources)}")
        logger.info(f"Confidence: {response.confidence}")
        logger.info(f"Follow-up questions: {len(response.follow_up_questions)}")
        
        pipeline.close()
        return True
        
    except Exception as e:
        logger.error(f"RAG system test failed: {e}")
        return False


def test_api_endpoints():
    """Test API endpoints."""
    logger.info("Testing API endpoints...")
    
    try:
        from src.api.main import app
        from fastapi.testclient import TestClient
        
        client = TestClient(app)
        
        # Test health endpoint
        response = client.get("/health")
        if response.status_code != 200:
            logger.error("Health endpoint failed")
            return False
        
        logger.info("Health endpoint: OK")
        
        # Test search endpoint
        search_data = {
            "query": "definition",
            "limit": 3
        }
        response = client.post("/search", json=search_data)
        if response.status_code != 200:
            logger.error("Search endpoint failed")
            return False
        
        logger.info("Search endpoint: OK")
        
        return True
        
    except Exception as e:
        logger.error(f"API test failed: {e}")
        return False


def run_comprehensive_test():
    """Run all system tests."""
    logger.info("Starting comprehensive system test...")
    
    tests = [
        ("XML Parsing", test_xml_parsing),
        ("Vector Indexing", test_vector_indexing),
        ("Graph Database", test_graph_database),
        ("Processing Pipeline", test_pipeline),
        ("RAG System", test_rag_system),
        ("API Endpoints", test_api_endpoints),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running test: {test_name}")
        logger.info('='*50)
        
        try:
            result = test_func()
            results[test_name] = result
            status = "PASS" if result else "FAIL"
            logger.info(f"Test {test_name}: {status}")
        except Exception as e:
            logger.error(f"Test {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info('='*50)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "PASS" if result else "FAIL"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! System is ready to use.")
        return True
    else:
        logger.warning("⚠️  Some tests failed. Check configuration and dependencies.")
        return False


def main():
    """Main test function."""
    print("Hong Kong Legal XML RAG System - Comprehensive Test")
    print("="*60)
    
    # Check basic requirements
    logger.info("Checking basic requirements...")
    
    # Check if XML directory exists
    xml_dir = Path(settings.xml_dir)
    if not xml_dir.exists():
        logger.error(f"XML directory not found: {xml_dir}")
        logger.info("Please ensure XML files are available in the configured directory")
        return 1
    
    # Check for API keys
    if not settings.openai_api_key and not settings.anthropic_api_key:
        logger.warning("No LLM API keys configured - some features will be limited")
    
    # Run tests
    success = run_comprehensive_test()
    
    if success:
        logger.info("\n🚀 System is ready! You can now:")
        logger.info("  1. Run interactive demo: python -m src.app")
        logger.info("  2. Start API server: python -m src.app api")
        logger.info("  3. Use CLI interface: python -m src.app cli")
        return 0
    else:
        logger.error("\n❌ System has issues. Please check the logs above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
