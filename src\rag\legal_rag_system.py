"""Legal RAG system with LLM integration for Hong Kong legal documents."""

import logging
from typing import Any, Dict, List, Optional

from ..config import settings
from ..models import ChatMessage, ChatResponse, SearchQuery, SearchResult
from ..pipeline.processing_pipeline import LegalDocumentPipeline

logger = logging.getLogger(__name__)


class LegalRAGSystem:
    """RAG system specialized for Hong Kong legal documents."""
    
    def __init__(self, pipeline: LegalDocumentPipeline = None):
        self.pipeline = pipeline or LegalDocumentPipeline()
        self.llm_client = None
        self._initialize_llm()
        
        # System prompts for different types of queries
        self.system_prompts = {
            'general': """You are a Hong Kong legal AI assistant. You provide accurate, helpful information about Hong Kong law based on the provided legal documents. Always cite your sources and indicate when information may be incomplete or when users should consult a qualified lawyer.""",
            
            'definition': """You are a Hong Kong legal AI assistant specializing in legal definitions. When asked about legal terms, provide clear, accurate definitions based on the Hong Kong legal documents provided. Always cite the specific ordinance or regulation where the definition appears.""",
            
            'procedure': """You are a Hong Kong legal AI assistant specializing in legal procedures and regulations. When asked about procedures, provide step-by-step guidance based on the Hong Kong legal documents provided. Always cite relevant sections and warn users to verify current requirements.""",
            
            'amendment': """You are a Hong Kong legal AI assistant specializing in legal amendments and changes. When asked about changes to law, provide information about amendments, their effective dates, and their impact based on the provided documents."""
        }
    
    def _initialize_llm(self):
        """Initialize LLM client based on available API keys."""
        try:
            if settings.openai_api_key:
                import openai
                self.llm_client = openai.OpenAI(api_key=settings.openai_api_key)
                self.llm_provider = "openai"
                logger.info("Initialized OpenAI client")
            elif settings.anthropic_api_key:
                import anthropic
                self.llm_client = anthropic.Anthropic(api_key=settings.anthropic_api_key)
                self.llm_provider = "anthropic"
                logger.info("Initialized Anthropic client")
            else:
                logger.warning("No LLM API keys available")
                self.llm_provider = None
        except ImportError as e:
            logger.error(f"Failed to import LLM client: {e}")
            self.llm_provider = None
    
    def chat(self, message: str, conversation_history: List[ChatMessage] = None) -> ChatResponse:
        """Process a chat message and return a response with sources."""
        if not self.llm_client:
            return ChatResponse(
                message="LLM service not available. Please configure API keys.",
                confidence=0.0
            )
        
        try:
            # Determine query type and get appropriate context
            query_type = self._classify_query(message)
            
            # Search for relevant documents
            search_results = self._search_relevant_content(message, query_type)
            
            # Generate response using LLM
            response = self._generate_response(message, search_results, query_type, conversation_history)
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing chat message: {e}")
            return ChatResponse(
                message="I apologize, but I encountered an error processing your request. Please try again.",
                confidence=0.0
            )
    
    def _classify_query(self, message: str) -> str:
        """Classify the type of legal query."""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ['define', 'definition', 'meaning', 'what is', 'what does', 'means']):
            return 'definition'
        elif any(word in message_lower for word in ['procedure', 'process', 'how to', 'steps', 'apply', 'application']):
            return 'procedure'
        elif any(word in message_lower for word in ['amendment', 'changed', 'updated', 'modified', 'when did']):
            return 'amendment'
        else:
            return 'general'
    
    def _search_relevant_content(self, query: str, query_type: str) -> List[SearchResult]:
        """Search for relevant content based on query type."""
        search_filters = {}
        
        # Adjust search based on query type
        if query_type == 'definition':
            search_filters['content_type'] = 'primary'
            search_filters['element_type'] = 'definition'
        elif query_type == 'amendment':
            search_filters['content_type'] = 'amendment'
        
        search_query = SearchQuery(
            query=query,
            limit=10,
            filters=search_filters,
            include_definitions=True,
            include_sections=True,
            include_schedules=query_type in ['procedure', 'general']
        )
        
        return self.pipeline.vector_manager.search(search_query)
    
    def _generate_response(self, message: str, search_results: List[SearchResult], 
                          query_type: str, conversation_history: List[ChatMessage] = None) -> ChatResponse:
        """Generate response using LLM with retrieved context."""
        
        # Prepare context from search results
        context = self._prepare_context(search_results)
        
        # Build conversation messages
        messages = self._build_conversation(message, context, query_type, conversation_history)
        
        # Generate response
        if self.llm_provider == "openai":
            response = self._generate_openai_response(messages)
        elif self.llm_provider == "anthropic":
            response = self._generate_anthropic_response(messages)
        else:
            return ChatResponse(
                message="LLM service not configured",
                confidence=0.0
            )
        
        # Calculate confidence based on search results quality
        confidence = self._calculate_confidence(search_results)
        
        # Generate follow-up questions
        follow_up_questions = self._generate_follow_up_questions(message, query_type)
        
        return ChatResponse(
            message=response,
            sources=search_results[:5],  # Top 5 sources
            confidence=confidence,
            follow_up_questions=follow_up_questions
        )
    
    def _prepare_context(self, search_results: List[SearchResult]) -> str:
        """Prepare context string from search results."""
        context_parts = []
        
        for i, result in enumerate(search_results[:5]):  # Top 5 results
            source_info = f"Source {i+1}: {result.document}"
            if result.section:
                source_info += f", {result.section}"
            
            context_part = f"{source_info}\nContent: {result.content}\n"
            context_parts.append(context_part)
        
        return "\n".join(context_parts)
    
    def _build_conversation(self, message: str, context: str, query_type: str, 
                           conversation_history: List[ChatMessage] = None) -> List[Dict[str, str]]:
        """Build conversation messages for LLM."""
        messages = [
            {"role": "system", "content": self.system_prompts[query_type]}
        ]
        
        # Add conversation history if available
        if conversation_history:
            for chat_msg in conversation_history[-5:]:  # Last 5 messages
                messages.append({
                    "role": chat_msg.role,
                    "content": chat_msg.content
                })
        
        # Add current query with context
        user_message = f"""Based on the following Hong Kong legal documents, please answer this question: {message}

Legal Document Context:
{context}

Please provide a clear, accurate answer based on the provided legal documents. Always cite your sources using the document names provided. If the information is incomplete or if legal advice is needed, please indicate that the user should consult a qualified Hong Kong lawyer."""
        
        messages.append({"role": "user", "content": user_message})
        
        return messages
    
    def _generate_openai_response(self, messages: List[Dict[str, str]]) -> str:
        """Generate response using OpenAI."""
        try:
            response = self.llm_client.chat.completions.create(
                model="gpt-4",
                messages=messages,
                max_tokens=1000,
                temperature=0.1
            )
            return response.choices[0].message.content
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            return "I apologize, but I'm having trouble generating a response right now."
    
    def _generate_anthropic_response(self, messages: List[Dict[str, str]]) -> str:
        """Generate response using Anthropic Claude."""
        try:
            # Convert messages format for Anthropic
            system_message = messages[0]["content"]
            conversation_messages = messages[1:]
            
            response = self.llm_client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=1000,
                temperature=0.1,
                system=system_message,
                messages=conversation_messages
            )
            return response.content[0].text
        except Exception as e:
            logger.error(f"Anthropic API error: {e}")
            return "I apologize, but I'm having trouble generating a response right now."
    
    def _calculate_confidence(self, search_results: List[SearchResult]) -> float:
        """Calculate confidence score based on search results quality."""
        if not search_results:
            return 0.0
        
        # Base confidence on top result score and number of results
        top_score = search_results[0].score if search_results else 0.0
        result_count = len(search_results)
        
        # Higher confidence with better scores and more results
        confidence = min(0.9, top_score * 0.8 + (result_count / 10) * 0.2)
        
        return round(confidence, 2)
    
    def _generate_follow_up_questions(self, message: str, query_type: str) -> List[str]:
        """Generate relevant follow-up questions."""
        follow_ups = []
        
        if query_type == 'definition':
            follow_ups = [
                "Are there any related terms I should know about?",
                "How is this term used in practice?",
                "Are there any recent amendments to this definition?"
            ]
        elif query_type == 'procedure':
            follow_ups = [
                "What are the required documents for this procedure?",
                "What are the fees involved?",
                "How long does this process typically take?"
            ]
        elif query_type == 'amendment':
            follow_ups = [
                "What was the previous version of this law?",
                "When did this amendment take effect?",
                "Are there any transitional provisions?"
            ]
        else:
            follow_ups = [
                "Can you provide more specific information?",
                "Are there any related regulations?",
                "What are the practical implications?"
            ]
        
        return follow_ups[:3]  # Return top 3
