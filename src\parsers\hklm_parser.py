"""
Hong Kong Legislation Model (HKLM) XML Parser

This module provides comprehensive parsing capabilities for Hong Kong legal documents
following the HKLM XML schema as defined in the data dictionary.
"""
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from lxml import etree
from bs4 import BeautifulSoup, Tag

logger = logging.getLogger(__name__)


@dataclass
class LegalMetadata:
    """Metadata extracted from legal documents"""
    doc_name: Optional[str] = None
    doc_type: Optional[str] = None
    doc_number: Optional[str] = None
    doc_status: Optional[str] = None
    identifier: Optional[str] = None
    date: Optional[str] = None
    subject: Optional[str] = None
    language: Optional[str] = None
    publisher: Optional[str] = None
    rights: Optional[str] = None


@dataclass
class LegalSection:
    """Represents a section in legal document"""
    id: Optional[str] = None
    name: Optional[str] = None
    num: Optional[str] = None
    heading: Optional[str] = None
    content: str = ""
    subsections: List['LegalSection'] = field(default_factory=list)
    parent_id: Optional[str] = None
    level: str = "section"  # section, subsection, paragraph, etc.
    temporal_id: Optional[str] = None
    status: Optional[str] = None
    reason: Optional[str] = None


@dataclass
class LegalDocument:
    """Complete legal document structure"""
    metadata: LegalMetadata
    long_title: Optional[str] = None
    short_title: Optional[str] = None
    commencement_note: Optional[str] = None
    sections: List[LegalSection] = field(default_factory=list)
    definitions: Dict[str, str] = field(default_factory=dict)
    cross_references: List[Dict[str, str]] = field(default_factory=list)
    amendments: List[Dict[str, Any]] = field(default_factory=list)
    schedules: List[Dict[str, Any]] = field(default_factory=list)


class HKLMParser:
    """Parser for Hong Kong Legislation Model XML documents"""
    
    def __init__(self):
        self.namespaces = {
            'hklm': 'http://www.xml.gov.hk/schemas/hklm/1.0',
            'dc': 'http://purl.org/dc/elements/1.1/',
            'dcterms': 'http://purl.org/dc/terms/',
            'xhtml': 'http://www.w3.org/1999/xhtml'
        }
    
    def parse_file(self, file_path: Union[str, Path]) -> Optional[LegalDocument]:
        """Parse a single HKLM XML file"""
        try:
            file_path = Path(file_path)
            if not file_path.exists():
                logger.error(f"File not found: {file_path}")
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return self.parse_content(content)
            
        except Exception as e:
            logger.error(f"Error parsing file {file_path}: {e}")
            return None
    
    def parse_content(self, xml_content: str) -> Optional[LegalDocument]:
        """Parse XML content string"""
        try:
            # Use BeautifulSoup for more flexible parsing
            soup = BeautifulSoup(xml_content, 'xml')
            
            # Extract metadata
            metadata = self._extract_metadata(soup)
            
            # Create document structure
            document = LegalDocument(metadata=metadata)
            
            # Extract main content
            self._extract_titles(soup, document)
            self._extract_commencement(soup, document)
            self._extract_sections(soup, document)
            self._extract_definitions(soup, document)
            self._extract_cross_references(soup, document)
            
            return document
            
        except Exception as e:
            logger.error(f"Error parsing XML content: {e}")
            return None

    def _extract_metadata(self, soup: BeautifulSoup) -> LegalMetadata:
        """Extract document metadata"""
        metadata = LegalMetadata()

        meta_section = soup.find('meta')
        if meta_section:
            metadata.doc_name = self._get_text_content(meta_section.find('docName'))
            metadata.doc_type = self._get_text_content(meta_section.find('docType'))
            metadata.doc_number = self._get_text_content(meta_section.find('docNumber'))
            metadata.doc_status = self._get_text_content(meta_section.find('docStatus'))

            # Dublin Core metadata
            metadata.identifier = self._get_text_content(meta_section.find('dc:identifier'))
            metadata.date = self._get_text_content(meta_section.find('dc:date'))
            metadata.subject = self._get_text_content(meta_section.find('dc:subject'))
            metadata.language = self._get_text_content(meta_section.find('dc:language'))
            metadata.publisher = self._get_text_content(meta_section.find('dc:publisher'))
            metadata.rights = self._get_text_content(meta_section.find('dc:rights'))

        return metadata

    def _extract_titles(self, soup: BeautifulSoup, document: LegalDocument):
        """Extract document titles"""
        long_title = soup.find('longTitle')
        if long_title:
            document.long_title = self._get_text_content(long_title)

        short_title = soup.find('shortTitle')
        if short_title:
            document.short_title = self._get_text_content(short_title)

    def _extract_commencement(self, soup: BeautifulSoup, document: LegalDocument):
        """Extract commencement note"""
        commencement = soup.find('commencementNote')
        if commencement:
            document.commencement_note = self._get_text_content(commencement)

    def _extract_sections(self, soup: BeautifulSoup, document: LegalDocument):
        """Extract all sections and hierarchical content"""
        main_content = soup.find('main')
        if not main_content:
            return

        # Extract parts and sections
        parts = main_content.find_all('part')
        for part in parts:
            part_section = self._parse_section_element(part, level="part")
            if part_section:
                document.sections.append(part_section)

                # Extract sections within part
                sections = part.find_all('section', recursive=False)
                for section in sections:
                    section_obj = self._parse_section_element(section, level="section", parent_id=part_section.id)
                    if section_obj:
                        part_section.subsections.append(section_obj)
                        self._extract_subsections(section, section_obj)

        # Extract standalone sections (not within parts)
        standalone_sections = main_content.find_all('section', recursive=False)
        for section in standalone_sections:
            section_obj = self._parse_section_element(section, level="section")
            if section_obj:
                document.sections.append(section_obj)
                self._extract_subsections(section, section_obj)

    def _extract_subsections(self, parent_element: Tag, parent_section: LegalSection):
        """Recursively extract subsections"""
        subsections = parent_element.find_all('subsection', recursive=False)
        for subsection in subsections:
            subsection_obj = self._parse_section_element(subsection, level="subsection", parent_id=parent_section.id)
            if subsection_obj:
                parent_section.subsections.append(subsection_obj)

                # Extract paragraphs
                paragraphs = subsection.find_all('paragraph', recursive=False)
                for paragraph in paragraphs:
                    para_obj = self._parse_section_element(paragraph, level="paragraph", parent_id=subsection_obj.id)
                    if para_obj:
                        subsection_obj.subsections.append(para_obj)

                        # Extract subparagraphs
                        subparas = paragraph.find_all('subparagraph', recursive=False)
                        for subpara in subparas:
                            subpara_obj = self._parse_section_element(subpara, level="subparagraph", parent_id=para_obj.id)
                            if subpara_obj:
                                para_obj.subsections.append(subpara_obj)

    def _parse_section_element(self, element: Tag, level: str, parent_id: Optional[str] = None) -> Optional[LegalSection]:
        """Parse a section-like element (part, section, subsection, etc.)"""
        if not element:
            return None

        section = LegalSection(level=level, parent_id=parent_id)

        # Extract attributes
        section.id = element.get('id')
        section.name = element.get('name')
        section.temporal_id = element.get('temporalId')
        section.status = element.get('status')
        section.reason = element.get('reason')

        # Extract number
        num_element = element.find('num')
        if num_element:
            section.num = self._get_text_content(num_element)

        # Extract heading
        heading_element = element.find('heading')
        if heading_element:
            section.heading = self._get_text_content(heading_element)

        # Extract content
        content_elements = element.find_all('content', recursive=False)
        content_parts = []
        for content_elem in content_elements:
            content_text = self._get_text_content(content_elem)
            if content_text:
                content_parts.append(content_text)

        section.content = ' '.join(content_parts)

        return section

    def _extract_definitions(self, soup: BeautifulSoup, document: LegalDocument):
        """Extract definitions from the document"""
        definitions = soup.find_all('def')
        for def_element in definitions:
            name_attr = def_element.get('name')
            if name_attr:
                term_element = def_element.find('term')
                term_text = self._get_text_content(term_element) if term_element else name_attr

                # Get definition content (excluding the term itself)
                def_content = self._get_text_content(def_element)
                if term_text and def_content:
                    document.definitions[term_text] = def_content

    def _extract_cross_references(self, soup: BeautifulSoup, document: LegalDocument):
        """Extract cross-references and citations"""
        refs = soup.find_all('ref')
        for ref in refs:
            href = ref.get('href')
            ref_text = self._get_text_content(ref)
            if href or ref_text:
                document.cross_references.append({
                    'href': href,
                    'text': ref_text,
                    'type': 'reference'
                })

    def _get_text_content(self, element: Optional[Tag]) -> Optional[str]:
        """Extract clean text content from an element"""
        if not element:
            return None

        # Get text and clean it up
        text = element.get_text(separator=' ', strip=True)
        if text:
            # Remove extra whitespace
            text = ' '.join(text.split())
            return text

        return None

    def parse_directory(self, directory_path: Union[str, Path]) -> List[LegalDocument]:
        """Parse all XML files in a directory"""
        directory_path = Path(directory_path)
        documents = []

        if not directory_path.exists():
            logger.error(f"Directory not found: {directory_path}")
            return documents

        xml_files = list(directory_path.rglob("*.xml"))
        logger.info(f"Found {len(xml_files)} XML files in {directory_path}")

        for xml_file in xml_files:
            document = self.parse_file(xml_file)
            if document:
                documents.append(document)
                logger.debug(f"Successfully parsed: {xml_file}")
            else:
                logger.warning(f"Failed to parse: {xml_file}")

        logger.info(f"Successfully parsed {len(documents)} documents")
        return documents
