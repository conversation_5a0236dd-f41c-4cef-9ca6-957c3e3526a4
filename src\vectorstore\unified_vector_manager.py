"""Unified vector database manager supporting both ChromaDB and Qdrant."""

import logging
from enum import Enum
from typing import Any, Dict, List, Optional

from ..config import settings
from ..models import ExtractedContent, SearchQuery, SearchResult

logger = logging.getLogger(__name__)


class VectorDatabaseType(str, Enum):
    """Supported vector database types."""
    CHROMADB = "chromadb"
    QDRANT = "qdrant"


class UnifiedVectorManager:
    """Unified vector manager supporting multiple vector database backends."""
    
    def __init__(self, 
                 db_type: Optional[VectorDatabaseType] = None,
                 use_openai_embeddings: bool = True,
                 **kwargs):
        """
        Initialize unified vector manager.
        
        Args:
            db_type: Vector database type (auto-detect if None)
            use_openai_embeddings: Whether to use OpenAI embeddings
            **kwargs: Additional configuration for specific databases
        """
        self.db_type = db_type or self._detect_database_type()
        self.use_openai_embeddings = use_openai_embeddings and bool(settings.openai_api_key)
        
        # Initialize the appropriate backend
        if self.db_type == VectorDatabaseType.QDRANT:
            self._initialize_qdrant(**kwargs)
        else:
            self._initialize_chromadb(**kwargs)
        
        logger.info(f"Initialized unified vector manager with {self.db_type.value} backend")
        logger.info(f"Using OpenAI embeddings: {self.use_openai_embeddings}")
    
    def _detect_database_type(self) -> VectorDatabaseType:
        """Auto-detect which vector database to use based on configuration."""
        # Check for Qdrant configuration
        if hasattr(settings, 'qdrant_host') or hasattr(settings, 'qdrant_url'):
            return VectorDatabaseType.QDRANT
        
        # Check environment variables
        import os
        if os.getenv('QDRANT_HOST') or os.getenv('QDRANT_URL'):
            return VectorDatabaseType.QDRANT
        
        # Default to ChromaDB
        return VectorDatabaseType.CHROMADB
    
    def _initialize_qdrant(self, **kwargs):
        """Initialize Qdrant backend."""
        try:
            from .qdrant_manager import QdrantVectorManager
            
            # Get Qdrant configuration
            qdrant_config = {
                'host': getattr(settings, 'qdrant_host', kwargs.get('host', 'localhost')),
                'port': getattr(settings, 'qdrant_port', kwargs.get('port', 6333)),
                'api_key': getattr(settings, 'qdrant_api_key', kwargs.get('api_key')),
                'use_openai_embeddings': self.use_openai_embeddings
            }
            
            self.backend = QdrantVectorManager(**qdrant_config)
            logger.info("Initialized Qdrant backend")
            
        except ImportError:
            logger.error("Qdrant client not available. Install with: pip install qdrant-client")
            raise
        except Exception as e:
            logger.error(f"Failed to initialize Qdrant: {e}")
            logger.info("Falling back to ChromaDB")
            self.db_type = VectorDatabaseType.CHROMADB
            self._initialize_chromadb(**kwargs)
    
    def _initialize_chromadb(self, **kwargs):
        """Initialize ChromaDB backend."""
        try:
            if self.use_openai_embeddings:
                # Use ChromaDB with OpenAI embeddings
                from .chromadb_openai_manager import ChromaDBOpenAIManager
                self.backend = ChromaDBOpenAIManager(**kwargs)
            else:
                # Use existing comprehensive ChromaDB manager
                from .comprehensive_vector_manager import ComprehensiveVectorManager
                self.backend = ComprehensiveVectorManager(**kwargs)
            
            logger.info("Initialized ChromaDB backend")
            
        except Exception as e:
            logger.error(f"Failed to initialize ChromaDB: {e}")
            raise
    
    def add_extracted_content(self, extracted_content: ExtractedContent):
        """Add extracted content to vector database."""
        try:
            # Add definitions
            if hasattr(self.backend, 'add_definitions'):
                self.backend.add_definitions(extracted_content)
            
            # Add sections
            if hasattr(self.backend, 'add_sections'):
                self.backend.add_sections(extracted_content)
            
            # Add schedules
            if hasattr(self.backend, 'add_schedules'):
                self.backend.add_schedules(extracted_content)
            
            # Add amendments
            if hasattr(self.backend, 'add_amendments'):
                self.backend.add_amendments(extracted_content)
            
            # Add references
            if hasattr(self.backend, 'add_references'):
                self.backend.add_references(extracted_content)
            
            logger.info(f"Added extracted content from {extracted_content.document_metadata.doc_name}")
            
        except Exception as e:
            logger.error(f"Failed to add extracted content: {e}")
            raise
    
    def search(self, query: SearchQuery) -> List[SearchResult]:
        """Search vector database."""
        try:
            return self.backend.search(query)
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []
    
    def search_by_metadata(self, filters: Dict[str, Any], limit: int = 10) -> List[Dict]:
        """Search by metadata filters."""
        try:
            return self.backend.search_by_metadata(filters, limit)
        except Exception as e:
            logger.error(f"Metadata search failed: {e}")
            return []
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get collection statistics."""
        try:
            stats = self.backend.get_collection_stats()
            stats['database_type'] = self.db_type.value
            stats['openai_embeddings'] = self.use_openai_embeddings
            return stats
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {'error': str(e)}
    
    def health_check(self) -> Dict[str, Any]:
        """Check vector database health."""
        try:
            health = self.backend.health_check()
            health['database_type'] = self.db_type.value
            health['openai_embeddings'] = self.use_openai_embeddings
            return health
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'database_type': self.db_type.value,
                'openai_embeddings': self.use_openai_embeddings
            }
    
    def clear_all_collections(self) -> bool:
        """Clear all collections."""
        try:
            if hasattr(self.backend, 'clear_all_collections'):
                return self.backend.clear_all_collections()
            else:
                # Try to clear individual collections
                collections = ['definitions', 'sections', 'schedules', 'amendments', 'references']
                success = True
                for collection in collections:
                    if hasattr(self.backend, 'delete_collection'):
                        success &= self.backend.delete_collection(collection)
                return success
        except Exception as e:
            logger.error(f"Failed to clear collections: {e}")
            return False
    
    def close(self):
        """Close vector database connection."""
        try:
            if hasattr(self.backend, 'close'):
                self.backend.close()
            logger.info("Closed vector database connection")
        except Exception as e:
            logger.error(f"Error closing vector database: {e}")
    
    def get_backend_info(self) -> Dict[str, Any]:
        """Get information about the current backend."""
        info = {
            'database_type': self.db_type.value,
            'openai_embeddings': self.use_openai_embeddings,
            'backend_class': self.backend.__class__.__name__
        }
        
        # Add backend-specific info
        if hasattr(self.backend, 'get_model_info'):
            info['embedding_model'] = self.backend.get_model_info()
        
        if hasattr(self.backend, 'embedding_dimension'):
            info['embedding_dimension'] = self.backend.embedding_dimension
        
        return info


class VectorManagerFactory:
    """Factory for creating vector managers with different configurations."""
    
    @staticmethod
    def create_production_manager() -> UnifiedVectorManager:
        """Create a production-ready vector manager."""
        return UnifiedVectorManager(
            db_type=VectorDatabaseType.QDRANT,
            use_openai_embeddings=True,
            host=getattr(settings, 'qdrant_host', 'localhost'),
            port=getattr(settings, 'qdrant_port', 6333),
            api_key=getattr(settings, 'qdrant_api_key', None)
        )
    
    @staticmethod
    def create_development_manager() -> UnifiedVectorManager:
        """Create a development vector manager."""
        return UnifiedVectorManager(
            db_type=VectorDatabaseType.CHROMADB,
            use_openai_embeddings=bool(settings.openai_api_key)
        )
    
    @staticmethod
    def create_testing_manager() -> UnifiedVectorManager:
        """Create a testing vector manager."""
        return UnifiedVectorManager(
            db_type=VectorDatabaseType.CHROMADB,
            use_openai_embeddings=False,
            persist_directory="./test_chroma_db"
        )
    
    @staticmethod
    def create_cloud_manager(qdrant_url: str, api_key: str) -> UnifiedVectorManager:
        """Create a cloud-based vector manager."""
        return UnifiedVectorManager(
            db_type=VectorDatabaseType.QDRANT,
            use_openai_embeddings=True,
            host=qdrant_url,
            api_key=api_key
        )


# Convenience function for backward compatibility
def get_vector_manager(**kwargs) -> UnifiedVectorManager:
    """Get a vector manager instance with automatic configuration."""
    return UnifiedVectorManager(**kwargs)
