"""Authentication and authorization system for Hong Kong Legal RAG Backend Service."""

import hashlib
import secrets
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional

import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from passlib.context import Crypt<PERSON>ontext
from pydantic import BaseModel

from ..config import settings

# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT settings
SECRET_KEY = settings.secret_key if hasattr(settings, 'secret_key') else secrets.token_urlsafe(32)
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
REFRESH_TOKEN_EXPIRE_DAYS = 7

# Security scheme
security = HTTPBearer()


class UserRole(str, Enum):
    """User roles with different access levels."""
    CITIZEN = "citizen"
    LAWYER = "lawyer"
    GOVERNMENT_OFFICIAL = "government_official"
    JUDGE = "judge"
    LEGAL_RESEARCHER = "legal_researcher"
    ADMIN = "admin"


class UserPermission(str, Enum):
    """Granular permissions for legal operations."""
    READ_PUBLIC_LAWS = "read_public_laws"
    READ_RESTRICTED_LAWS = "read_restricted_laws"
    GENERATE_LEGAL_DOCUMENTS = "generate_legal_documents"
    ACCESS_CASE_LAW = "access_case_law"
    VERIFY_CITATIONS = "verify_citations"
    EXPORT_LEGAL_REPORTS = "export_legal_reports"
    MANAGE_USERS = "manage_users"
    VIEW_AUDIT_LOGS = "view_audit_logs"
    ADVANCED_LEGAL_RESEARCH = "advanced_legal_research"
    CONSTITUTIONAL_LAW_ACCESS = "constitutional_law_access"


class User(BaseModel):
    """User model with legal-specific attributes."""
    id: str
    username: str
    email: str
    full_name: str
    role: UserRole
    permissions: List[UserPermission]
    law_firm: Optional[str] = None
    bar_number: Optional[str] = None
    government_department: Optional[str] = None
    is_active: bool = True
    created_at: datetime
    last_login: Optional[datetime] = None
    
    # Legal practice specific fields
    practice_areas: List[str] = []
    jurisdiction: str = "Hong Kong"
    language_preference: str = "en"


class TokenData(BaseModel):
    """Token payload data."""
    user_id: str
    username: str
    role: UserRole
    permissions: List[UserPermission]
    exp: datetime


# Role-based permissions mapping
ROLE_PERMISSIONS = {
    UserRole.CITIZEN: [
        UserPermission.READ_PUBLIC_LAWS,
    ],
    UserRole.LAWYER: [
        UserPermission.READ_PUBLIC_LAWS,
        UserPermission.READ_RESTRICTED_LAWS,
        UserPermission.GENERATE_LEGAL_DOCUMENTS,
        UserPermission.ACCESS_CASE_LAW,
        UserPermission.VERIFY_CITATIONS,
        UserPermission.EXPORT_LEGAL_REPORTS,
        UserPermission.ADVANCED_LEGAL_RESEARCH,
    ],
    UserRole.GOVERNMENT_OFFICIAL: [
        UserPermission.READ_PUBLIC_LAWS,
        UserPermission.READ_RESTRICTED_LAWS,
        UserPermission.ACCESS_CASE_LAW,
        UserPermission.VERIFY_CITATIONS,
        UserPermission.EXPORT_LEGAL_REPORTS,
        UserPermission.ADVANCED_LEGAL_RESEARCH,
        UserPermission.CONSTITUTIONAL_LAW_ACCESS,
    ],
    UserRole.JUDGE: [
        UserPermission.READ_PUBLIC_LAWS,
        UserPermission.READ_RESTRICTED_LAWS,
        UserPermission.ACCESS_CASE_LAW,
        UserPermission.VERIFY_CITATIONS,
        UserPermission.EXPORT_LEGAL_REPORTS,
        UserPermission.ADVANCED_LEGAL_RESEARCH,
        UserPermission.CONSTITUTIONAL_LAW_ACCESS,
        UserPermission.VIEW_AUDIT_LOGS,
    ],
    UserRole.LEGAL_RESEARCHER: [
        UserPermission.READ_PUBLIC_LAWS,
        UserPermission.READ_RESTRICTED_LAWS,
        UserPermission.ACCESS_CASE_LAW,
        UserPermission.VERIFY_CITATIONS,
        UserPermission.ADVANCED_LEGAL_RESEARCH,
    ],
    UserRole.ADMIN: [perm for perm in UserPermission],
}


class AuthService:
    """Authentication and authorization service."""
    
    def __init__(self):
        self.users_db: Dict[str, User] = {}
        self.sessions: Dict[str, TokenData] = {}
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash."""
        return pwd_context.verify(plain_password, hashed_password)
    
    def get_password_hash(self, password: str) -> str:
        """Generate password hash."""
        return pwd_context.hash(password)
    
    def create_access_token(self, user: User) -> str:
        """Create JWT access token."""
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode = {
            "user_id": user.id,
            "username": user.username,
            "role": user.role.value,
            "permissions": [perm.value for perm in user.permissions],
            "exp": expire
        }
        
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    def create_refresh_token(self, user: User) -> str:
        """Create JWT refresh token."""
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        
        to_encode = {
            "user_id": user.id,
            "type": "refresh",
            "exp": expire
        }
        
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    def verify_token(self, token: str) -> TokenData:
        """Verify and decode JWT token."""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            
            user_id: str = payload.get("user_id")
            username: str = payload.get("username")
            role: str = payload.get("role")
            permissions: List[str] = payload.get("permissions", [])
            exp: datetime = datetime.fromtimestamp(payload.get("exp"))
            
            if user_id is None or username is None:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid token"
                )
            
            return TokenData(
                user_id=user_id,
                username=username,
                role=UserRole(role),
                permissions=[UserPermission(perm) for perm in permissions],
                exp=exp
            )
            
        except jwt.PyJWTError:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
    
    def get_user_permissions(self, role: UserRole) -> List[UserPermission]:
        """Get permissions for a user role."""
        return ROLE_PERMISSIONS.get(role, [])
    
    def check_permission(self, user_permissions: List[UserPermission], 
                        required_permission: UserPermission) -> bool:
        """Check if user has required permission."""
        return required_permission in user_permissions


# Global auth service instance
auth_service = AuthService()


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> TokenData:
    """Get current authenticated user."""
    token = credentials.credentials
    return auth_service.verify_token(token)


def require_permission(permission: UserPermission):
    """Decorator to require specific permission."""
    def permission_checker(current_user: TokenData = Depends(get_current_user)) -> TokenData:
        if not auth_service.check_permission(current_user.permissions, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient permissions. Required: {permission.value}"
            )
        return current_user
    
    return permission_checker


def require_role(role: UserRole):
    """Decorator to require specific role."""
    def role_checker(current_user: TokenData = Depends(get_current_user)) -> TokenData:
        if current_user.role != role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Insufficient role. Required: {role.value}"
            )
        return current_user
    
    return role_checker


class AuditLogger:
    """Audit logging for legal compliance."""
    
    def __init__(self):
        self.audit_logs: List[Dict] = []
    
    def log_access(self, user_id: str, action: str, resource: str, 
                   ip_address: str, user_agent: str, success: bool = True):
        """Log user access for audit trail."""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "user_id": user_id,
            "action": action,
            "resource": resource,
            "ip_address": ip_address,
            "user_agent": user_agent,
            "success": success,
            "session_id": hashlib.md5(f"{user_id}{datetime.utcnow()}".encode()).hexdigest()
        }
        
        self.audit_logs.append(log_entry)
        
        # In production, this would write to a secure audit database
        # with tamper-proof logging for legal compliance
    
    def get_audit_logs(self, user_id: Optional[str] = None, 
                      start_date: Optional[datetime] = None,
                      end_date: Optional[datetime] = None) -> List[Dict]:
        """Retrieve audit logs with filtering."""
        logs = self.audit_logs
        
        if user_id:
            logs = [log for log in logs if log["user_id"] == user_id]
        
        if start_date:
            logs = [log for log in logs if datetime.fromisoformat(log["timestamp"]) >= start_date]
        
        if end_date:
            logs = [log for log in logs if datetime.fromisoformat(log["timestamp"]) <= end_date]
        
        return logs


# Global audit logger
audit_logger = AuditLogger()
