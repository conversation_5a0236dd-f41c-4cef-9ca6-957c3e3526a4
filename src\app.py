"""
Main application entry point for the Hong Kong Legal XML RAG System
"""

import logging
import sys
from pathlib import Path

from .config import settings
from .pipeline.processing_pipeline import LegalDocumentPipeline
from .rag.legal_rag_system import LegalRAGSystem

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main function for the Hong Kong Legal RAG System."""
    logger.info("Starting Hong Kong Legal RAG System")
    
    try:
        # Initialize the pipeline
        pipeline = LegalDocumentPipeline()
        
        # Check if XML directory exists
        xml_dir = settings.xml_dir
        if not xml_dir.exists():
            logger.error(f"XML directory not found: {xml_dir}")
            logger.info("Please ensure the XML files are in the correct directory")
            return 1
        
        # Process documents if they haven't been processed yet
        stats = pipeline.get_processing_stats()
        if stats['processed_documents'] == 0:
            logger.info(f"Processing documents from: {xml_dir}")
            processing_results = pipeline.process_directory(str(xml_dir))
            logger.info(f"Processing completed: {processing_results}")
        else:
            logger.info(f"Found {stats['processed_documents']} already processed documents")
        
        # Initialize RAG system
        rag_system = LegalRAGSystem(pipeline)
        
        # Interactive demo
        print("\n" + "="*60)
        print("Hong Kong Legal RAG System - Interactive Demo")
        print("="*60)
        print("Ask questions about Hong Kong law, or type 'quit' to exit")
        print("Examples:")
        print("- What is the definition of adult?")
        print("- How do I apply for a business license?")
        print("- What are the recent amendments to Cap. 1?")
        print("-"*60)
        
        conversation_history = []
        
        while True:
            try:
                user_input = input("\nYou: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not user_input:
                    continue
                
                # Get response from RAG system
                response = rag_system.chat(user_input, conversation_history)
                
                print(f"\nAssistant: {response.message}")
                
                if response.sources:
                    print(f"\nSources:")
                    for i, source in enumerate(response.sources[:3], 1):
                        print(f"  {i}. {source.document} - {source.element_type.value}")
                        print(f"     Score: {source.score:.3f}")
                
                if response.confidence < 0.5:
                    print("\n⚠️  Low confidence response. Consider consulting a qualified lawyer.")
                
                if response.follow_up_questions:
                    print("\nSuggested follow-up questions:")
                    for i, question in enumerate(response.follow_up_questions, 1):
                        print(f"  {i}. {question}")
                
                # Add to conversation history
                from .models import ChatMessage
                conversation_history.append(ChatMessage(role="user", content=user_input))
                conversation_history.append(ChatMessage(role="assistant", content=response.message))
                
                # Keep only last 10 messages
                if len(conversation_history) > 10:
                    conversation_history = conversation_history[-10:]
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"Error: {e}")
                logger.error(f"Error in main loop: {e}")
        
        print("\nThank you for using the Hong Kong Legal RAG System!")
        
        # Cleanup
        pipeline.close()
        return 0
        
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        return 1


def run_api():
    """Run the FastAPI server."""
    import uvicorn
    from .api.main import app
    
    logger.info("Starting FastAPI server")
    uvicorn.run(
        app,
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )


def run_cli():
    """Run the CLI interface."""
    from .cli import cli
    cli()


if __name__ == "__main__":
    # Check command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == "api":
            run_api()
        elif sys.argv[1] == "cli":
            run_cli()
        else:
            print("Usage: python -m src.app [api|cli]")
            print("  api - Start FastAPI server")
            print("  cli - Start CLI interface")
            print("  (no args) - Start interactive demo")
            sys.exit(1)
    else:
        sys.exit(main())
