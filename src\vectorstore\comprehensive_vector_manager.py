"""Comprehensive vector store management for Hong Kong legal documents."""

import logging
from typing import Any, Dict, List, Optional, Tuple

import chromadb
import numpy as np
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer

from ..config import settings
from ..models import ContentType, ElementType, ExtractedContent, SearchQuery, SearchResult

logger = logging.getLogger(__name__)


class ComprehensiveVectorManager:
    """Advanced vector store manager with legal document specialization."""
    
    def __init__(self, persist_directory: str = None):
        self.persist_directory = persist_directory or settings.chroma_persist_dir
        
        # Initialize ChromaDB client
        self.client = chromadb.PersistentClient(
            path=self.persist_directory,
            settings=Settings(anonymized_telemetry=False)
        )
        
        # Create specialized collections for different content types
        self.collections = {
            'definitions': self._get_or_create_collection('hk_legal_definitions'),
            'sections': self._get_or_create_collection('hk_legal_sections'),
            'schedules': self._get_or_create_collection('hk_legal_schedules'),
            'amendments': self._get_or_create_collection('hk_legal_amendments'),
            'references': self._get_or_create_collection('hk_legal_references')
        }
        
        # Initialize embedding models
        self.embedding_model = SentenceTransformer(settings.embedding_model)
        
        # For Chinese text processing
        try:
            self.chinese_model = SentenceTransformer('sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2')
        except Exception:
            logger.warning("Chinese embedding model not available, using default")
            self.chinese_model = self.embedding_model
    
    def _get_or_create_collection(self, name: str):
        """Get or create a collection with optimized settings."""
        return self.client.get_or_create_collection(
            name=name,
            metadata={"hnsw:space": "cosine", "hnsw:M": 16, "hnsw:ef_construction": 200}
        )
    
    def index_document(self, extracted_content: ExtractedContent) -> Dict[str, int]:
        """Index a complete extracted document with all content types."""
        indexed_counts = {
            'definitions': 0,
            'sections': 0,
            'schedules': 0,
            'amendments': 0,
            'references': 0
        }
        
        try:
            # Index definitions
            if extracted_content.definitions:
                self._index_definitions(extracted_content.definitions, extracted_content)
                indexed_counts['definitions'] = len(extracted_content.definitions)
            
            # Index sections
            if extracted_content.sections:
                self._index_sections(extracted_content.sections, extracted_content)
                indexed_counts['sections'] = len(extracted_content.sections)
            
            # Index schedules
            if extracted_content.schedules:
                self._index_schedules(extracted_content.schedules, extracted_content)
                indexed_counts['schedules'] = len(extracted_content.schedules)
            
            # Index amendments
            if extracted_content.amendments:
                self._index_amendments(extracted_content.amendments, extracted_content)
                indexed_counts['amendments'] = len(extracted_content.amendments)
            
            # Index references
            if extracted_content.references:
                self._index_references(extracted_content.references, extracted_content)
                indexed_counts['references'] = len(extracted_content.references)
            
            logger.info(f"Indexed document {extracted_content.file_path}: {indexed_counts}")
            return indexed_counts
            
        except Exception as e:
            logger.error(f"Error indexing document {extracted_content.file_path}: {e}")
            raise
    
    def _index_definitions(self, definitions: List, extracted_content: ExtractedContent):
        """Index legal definitions with specialized metadata."""
        texts = []
        metadatas = []
        ids = []
        
        for i, definition in enumerate(definitions):
            # Create comprehensive text for embedding
            text_parts = [definition.name]
            
            # Add terms in both languages
            for term in definition.terms:
                text_parts.append(f"{term.text} ({term.language})")
            
            # Add definition content
            if definition.content:
                text_parts.append(definition.content)
            
            # Add lead-in if available
            if definition.lead_in:
                text_parts.append(definition.lead_in)
            
            full_text = " ".join(text_parts)
            texts.append(full_text)
            
            # Create rich metadata
            metadata = {
                'content_type': ContentType.PRIMARY.value,
                'element_type': ElementType.DEFINITION.value,
                'document': extracted_content.document_metadata.doc_name or "Unknown",
                'document_type': extracted_content.document_type.value,
                'definition_name': definition.name,
                'file_path': extracted_content.file_path,
                'has_chinese': any(term.language.value == 'zh-Hant-HK' for term in definition.terms),
                'hierarchy_depth': len(definition.hierarchy),
                'has_references': len(definition.references) > 0,
                'status': definition.metadata.status or 'active'
            }
            
            # Add hierarchy information
            if definition.hierarchy:
                metadata['section'] = definition.hierarchy[-1].name
                metadata['hierarchy'] = " > ".join([h.element for h in definition.hierarchy])
            
            metadatas.append(metadata)
            ids.append(f"def_{extracted_content.content_hash}_{i}")
        
        if texts:
            self.collections['definitions'].add(
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
    
    def _index_sections(self, sections: List, extracted_content: ExtractedContent):
        """Index legal sections with hierarchical metadata."""
        texts = []
        metadatas = []
        ids = []
        
        for i, section in enumerate(sections):
            # Create text for embedding
            text_parts = []
            
            if section.heading:
                text_parts.append(section.heading)
            
            if section.content:
                text_parts.append(section.content)
            
            # Add subsection content
            for subsection in section.subsections:
                if subsection.get('content'):
                    text_parts.append(subsection['content'])
            
            full_text = " ".join(text_parts)
            texts.append(full_text)
            
            # Create metadata
            metadata = {
                'content_type': ContentType.PRIMARY.value,
                'element_type': ElementType.SECTION.value,
                'document': extracted_content.document_metadata.doc_name or "Unknown",
                'document_type': extracted_content.document_type.value,
                'section_id': section.section_id,
                'section_role': section.role or 'section',
                'file_path': extracted_content.file_path,
                'hierarchy_depth': len(section.hierarchy),
                'has_subsections': len(section.subsections) > 0,
                'has_references': len(section.references) > 0,
                'status': section.metadata.status or 'active'
            }
            
            # Add hierarchy information
            if section.hierarchy:
                metadata['hierarchy'] = " > ".join([h.element for h in section.hierarchy])
            
            metadatas.append(metadata)
            ids.append(f"sec_{extracted_content.content_hash}_{i}")
        
        if texts:
            self.collections['sections'].add(
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
    
    def _index_schedules(self, schedules: List, extracted_content: ExtractedContent):
        """Index schedules and appendices."""
        texts = []
        metadatas = []
        ids = []
        
        for i, schedule in enumerate(schedules):
            # Create text for embedding
            text_parts = []
            
            if schedule.heading:
                text_parts.append(schedule.heading)
            
            if schedule.content:
                text_parts.append(schedule.content)
            
            # Add layout content if available
            if schedule.layout:
                layout_text = self._extract_layout_text(schedule.layout)
                if layout_text:
                    text_parts.append(layout_text)
            
            full_text = " ".join(text_parts)
            texts.append(full_text)
            
            # Create metadata
            metadata = {
                'content_type': ContentType.STRUCTURAL.value,
                'element_type': ElementType.SCHEDULE.value,
                'document': extracted_content.document_metadata.doc_name or "Unknown",
                'document_type': extracted_content.document_type.value,
                'schedule_name': schedule.name,
                'file_path': extracted_content.file_path,
                'has_layout': schedule.layout is not None,
                'hierarchy_depth': len(schedule.hierarchy),
                'status': schedule.metadata.status or 'active'
            }
            
            # Add hierarchy information
            if schedule.hierarchy:
                metadata['hierarchy'] = " > ".join([h.element for h in schedule.hierarchy])
            
            metadatas.append(metadata)
            ids.append(f"sch_{extracted_content.content_hash}_{i}")
        
        if texts:
            self.collections['schedules'].add(
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
    
    def _extract_layout_text(self, layout: Dict[str, Any]) -> str:
        """Extract searchable text from layout structure."""
        text_parts = []
        
        # Extract header text
        for header in layout.get('headers', []):
            for column in header.get('columns', []):
                if column:
                    text_parts.append(column)
        
        # Extract row text
        for row in layout.get('rows', []):
            for column in row.get('columns', []):
                content = column.get('content', '')
                if content:
                    text_parts.append(content)
        
        return " ".join(text_parts)

    def _index_amendments(self, amendments: List, extracted_content: ExtractedContent):
        """Index amendment information."""
        texts = []
        metadatas = []
        ids = []

        for i, amendment in enumerate(amendments):
            # Create text for embedding
            text_parts = [amendment.source_note]

            if amendment.amending_document:
                text_parts.append(amendment.amending_document)

            # Add reference text
            for ref in amendment.references:
                text_parts.append(ref.text)

            full_text = " ".join(text_parts)
            texts.append(full_text)

            # Create metadata
            metadata = {
                'content_type': ContentType.AMENDMENT.value,
                'element_type': ElementType.NOTE.value,
                'document': extracted_content.document_metadata.doc_name or "Unknown",
                'document_type': extracted_content.document_type.value,
                'amendment_type': amendment.amendment_type,
                'amending_document': amendment.amending_document or "Unknown",
                'file_path': extracted_content.file_path,
                'has_references': len(amendment.references) > 0
            }

            metadatas.append(metadata)
            ids.append(f"amd_{extracted_content.content_hash}_{i}")

        if texts:
            self.collections['amendments'].add(
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )

    def _index_references(self, references: List, extracted_content: ExtractedContent):
        """Index reference information."""
        texts = []
        metadatas = []
        ids = []

        for i, reference in enumerate(references):
            # Create text for embedding
            text_parts = [reference.text]

            if reference.href:
                text_parts.append(reference.href)

            full_text = " ".join(text_parts)
            texts.append(full_text)

            # Create metadata
            metadata = {
                'content_type': ContentType.SECONDARY.value,
                'element_type': ElementType.REFERENCE.value,
                'document': extracted_content.document_metadata.doc_name or "Unknown",
                'document_type': extracted_content.document_type.value,
                'reference_type': reference.type,
                'href': reference.href or "",
                'idref': reference.idref or "",
                'file_path': extracted_content.file_path
            }

            metadatas.append(metadata)
            ids.append(f"ref_{extracted_content.content_hash}_{i}")

        if texts:
            self.collections['references'].add(
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )

    def search(self, query: SearchQuery) -> List[SearchResult]:
        """Comprehensive search across all content types."""
        all_results = []

        # Search each collection based on query preferences
        if query.include_definitions:
            def_results = self._search_collection('definitions', query)
            all_results.extend(def_results)

        if query.include_sections:
            sec_results = self._search_collection('sections', query)
            all_results.extend(sec_results)

        if query.include_schedules:
            sch_results = self._search_collection('schedules', query)
            all_results.extend(sch_results)

        # Always include amendments and references for context
        amd_results = self._search_collection('amendments', query)
        all_results.extend(amd_results)

        ref_results = self._search_collection('references', query)
        all_results.extend(ref_results)

        # Sort by score and limit results
        all_results.sort(key=lambda x: x.score, reverse=True)
        return all_results[:query.limit]

    def _search_collection(self, collection_name: str, query: SearchQuery) -> List[SearchResult]:
        """Search a specific collection."""
        try:
            collection = self.collections[collection_name]

            # Build where clause from filters
            where_clause = {}
            for key, value in query.filters.items():
                where_clause[key] = value

            # Add language filter if specified
            if query.language:
                where_clause['language'] = query.language.value

            # Perform search
            results = collection.query(
                query_texts=[query.query],
                n_results=min(query.limit, 50),  # Get more results for better ranking
                where=where_clause if where_clause else None
            )

            # Convert to SearchResult objects
            search_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    metadata = results['metadatas'][0][i] if results['metadatas'] else {}
                    distance = results['distances'][0][i] if results['distances'] else 0

                    # Convert distance to score (higher is better)
                    score = max(0, 1 - distance)

                    search_result = SearchResult(
                        content=doc,
                        content_type=ContentType(metadata.get('content_type', 'primary')),
                        element_type=ElementType(metadata.get('element_type', 'content')),
                        document=metadata.get('document', 'Unknown'),
                        section=metadata.get('section'),
                        score=score,
                        metadata=metadata
                    )
                    search_results.append(search_result)

            return search_results

        except Exception as e:
            logger.error(f"Error searching collection {collection_name}: {e}")
            return []

    def get_collection_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all collections."""
        stats = {}

        for name, collection in self.collections.items():
            try:
                count = collection.count()
                stats[name] = {
                    'count': count,
                    'name': collection.name
                }
            except Exception as e:
                logger.error(f"Error getting stats for collection {name}: {e}")
                stats[name] = {'count': 0, 'error': str(e)}

        return stats

    def delete_document(self, content_hash: str):
        """Delete all content for a specific document."""
        for collection in self.collections.values():
            try:
                # Get all IDs that start with the content hash
                all_items = collection.get()
                ids_to_delete = [
                    id for id in all_items['ids']
                    if id.split('_')[1] == content_hash
                ]

                if ids_to_delete:
                    collection.delete(ids=ids_to_delete)
                    logger.info(f"Deleted {len(ids_to_delete)} items from {collection.name}")

            except Exception as e:
                logger.error(f"Error deleting from collection {collection.name}: {e}")

    def clear_all_collections(self):
        """Clear all collections (use with caution)."""
        for name, collection in self.collections.items():
            try:
                # Delete and recreate collection
                self.client.delete_collection(collection.name)
                self.collections[name] = self._get_or_create_collection(collection.name)
                logger.info(f"Cleared collection: {name}")
            except Exception as e:
                logger.error(f"Error clearing collection {name}: {e}")
