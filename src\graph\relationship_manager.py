"""Graph database manager for legal document relationships."""

import logging
from typing import Any, Dict, List, Optional, Set, Tuple

from neo4j import GraphDatabase
from neo4j.exceptions import ServiceUnavailable

from ..config import settings
from ..models import ExtractedContent, Reference

logger = logging.getLogger(__name__)


class LegalRelationshipManager:
    """Manages legal document relationships using Neo4j graph database."""
    
    def __init__(self, uri: str = None, user: str = None, password: str = None):
        self.uri = uri or settings.neo4j_uri
        self.user = user or settings.neo4j_user
        self.password = password or settings.neo4j_password
        
        try:
            self.driver = GraphDatabase.driver(self.uri, auth=(self.user, self.password))
            self._verify_connectivity()
            self._create_constraints()
            logger.info("Connected to Neo4j database")
        except ServiceUnavailable as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            self.driver = None
    
    def _verify_connectivity(self):
        """Verify database connectivity."""
        with self.driver.session() as session:
            session.run("RETURN 1")
    
    def _create_constraints(self):
        """Create database constraints and indexes."""
        constraints = [
            "CREATE CONSTRAINT document_id IF NOT EXISTS FOR (d:Document) REQUIRE d.id IS UNIQUE",
            "CREATE CONSTRAINT definition_id IF NOT EXISTS FOR (def:Definition) REQUIRE def.id IS UNIQUE",
            "CREATE CONSTRAINT section_id IF NOT EXISTS FOR (s:Section) REQUIRE s.id IS UNIQUE",
            "CREATE CONSTRAINT schedule_id IF NOT EXISTS FOR (sch:Schedule) REQUIRE sch.id IS UNIQUE",
            "CREATE INDEX document_name IF NOT EXISTS FOR (d:Document) ON (d.name)",
            "CREATE INDEX definition_name IF NOT EXISTS FOR (def:Definition) ON (def.name)",
            "CREATE INDEX section_name IF NOT EXISTS FOR (s:Section) ON (s.name)"
        ]
        
        with self.driver.session() as session:
            for constraint in constraints:
                try:
                    session.run(constraint)
                except Exception as e:
                    logger.debug(f"Constraint/index already exists or failed: {e}")
    
    def index_document_relationships(self, extracted_content: ExtractedContent) -> Dict[str, int]:
        """Index all relationships for a document."""
        if not self.driver:
            logger.error("No database connection available")
            return {}
        
        relationship_counts = {
            'documents': 0,
            'definitions': 0,
            'sections': 0,
            'schedules': 0,
            'references': 0,
            'amendments': 0,
            'hierarchical': 0
        }
        
        try:
            with self.driver.session() as session:
                # Create document node
                doc_id = self._create_document_node(session, extracted_content)
                relationship_counts['documents'] = 1
                
                # Create definition nodes and relationships
                if extracted_content.definitions:
                    def_count = self._create_definition_nodes(session, extracted_content, doc_id)
                    relationship_counts['definitions'] = def_count
                
                # Create section nodes and relationships
                if extracted_content.sections:
                    sec_count = self._create_section_nodes(session, extracted_content, doc_id)
                    relationship_counts['sections'] = sec_count
                
                # Create schedule nodes and relationships
                if extracted_content.schedules:
                    sch_count = self._create_schedule_nodes(session, extracted_content, doc_id)
                    relationship_counts['schedules'] = sch_count
                
                # Create reference relationships
                ref_count = self._create_reference_relationships(session, extracted_content)
                relationship_counts['references'] = ref_count
                
                # Create amendment relationships
                amd_count = self._create_amendment_relationships(session, extracted_content)
                relationship_counts['amendments'] = amd_count
                
                # Create hierarchical relationships
                hier_count = self._create_hierarchical_relationships(session, extracted_content)
                relationship_counts['hierarchical'] = hier_count
                
                logger.info(f"Indexed relationships for {extracted_content.file_path}: {relationship_counts}")
                return relationship_counts
                
        except Exception as e:
            logger.error(f"Error indexing relationships for {extracted_content.file_path}: {e}")
            raise
    
    def _create_document_node(self, session, extracted_content: ExtractedContent) -> str:
        """Create document node."""
        doc_metadata = extracted_content.document_metadata
        
        query = """
        MERGE (d:Document {id: $doc_id})
        SET d.name = $name,
            d.type = $doc_type,
            d.number = $number,
            d.status = $status,
            d.title = $title,
            d.short_title = $short_title,
            d.file_path = $file_path,
            d.content_hash = $content_hash,
            d.extracted_at = $extracted_at
        RETURN d.id
        """
        
        doc_id = f"{doc_metadata.doc_name}_{extracted_content.content_hash}"
        
        result = session.run(query, {
            'doc_id': doc_id,
            'name': doc_metadata.doc_name or "Unknown",
            'doc_type': extracted_content.document_type.value,
            'number': doc_metadata.doc_number,
            'status': doc_metadata.doc_status,
            'title': doc_metadata.title,
            'short_title': doc_metadata.short_title,
            'file_path': extracted_content.file_path,
            'content_hash': extracted_content.content_hash,
            'extracted_at': extracted_content.extracted_at.isoformat()
        })
        
        return doc_id
    
    def _create_definition_nodes(self, session, extracted_content: ExtractedContent, doc_id: str) -> int:
        """Create definition nodes and relationships."""
        count = 0
        
        for i, definition in enumerate(extracted_content.definitions):
            def_id = f"{doc_id}_def_{i}"
            
            # Create definition node
            query = """
            MERGE (def:Definition {id: $def_id})
            SET def.name = $name,
                def.content = $content,
                def.lead_in = $lead_in,
                def.status = $status,
                def.hierarchy_depth = $hierarchy_depth
            WITH def
            MATCH (d:Document {id: $doc_id})
            MERGE (d)-[:CONTAINS_DEFINITION]->(def)
            """
            
            session.run(query, {
                'def_id': def_id,
                'name': definition.name,
                'content': definition.content,
                'lead_in': definition.lead_in,
                'status': definition.metadata.status or 'active',
                'hierarchy_depth': len(definition.hierarchy),
                'doc_id': doc_id
            })
            
            # Create term nodes
            for j, term in enumerate(definition.terms):
                term_id = f"{def_id}_term_{j}"
                
                term_query = """
                MERGE (t:Term {id: $term_id})
                SET t.text = $text,
                    t.language = $language
                WITH t
                MATCH (def:Definition {id: $def_id})
                MERGE (def)-[:HAS_TERM]->(t)
                """
                
                session.run(term_query, {
                    'term_id': term_id,
                    'text': term.text,
                    'language': term.language.value,
                    'def_id': def_id
                })
            
            count += 1
        
        return count
    
    def _create_section_nodes(self, session, extracted_content: ExtractedContent, doc_id: str) -> int:
        """Create section nodes and relationships."""
        count = 0
        
        for i, section in enumerate(extracted_content.sections):
            sec_id = f"{doc_id}_sec_{i}"
            
            # Create section node
            query = """
            MERGE (s:Section {id: $sec_id})
            SET s.section_id = $section_id,
                s.role = $role,
                s.heading = $heading,
                s.content = $content,
                s.status = $status,
                s.hierarchy_depth = $hierarchy_depth,
                s.has_subsections = $has_subsections
            WITH s
            MATCH (d:Document {id: $doc_id})
            MERGE (d)-[:CONTAINS_SECTION]->(s)
            """
            
            session.run(query, {
                'sec_id': sec_id,
                'section_id': section.section_id,
                'role': section.role or 'section',
                'heading': section.heading,
                'content': section.content,
                'status': section.metadata.status or 'active',
                'hierarchy_depth': len(section.hierarchy),
                'has_subsections': len(section.subsections) > 0,
                'doc_id': doc_id
            })
            
            count += 1
        
        return count
    
    def _create_schedule_nodes(self, session, extracted_content: ExtractedContent, doc_id: str) -> int:
        """Create schedule nodes and relationships."""
        count = 0
        
        for i, schedule in enumerate(extracted_content.schedules):
            sch_id = f"{doc_id}_sch_{i}"
            
            # Create schedule node
            query = """
            MERGE (sch:Schedule {id: $sch_id})
            SET sch.name = $name,
                sch.heading = $heading,
                sch.content = $content,
                sch.has_layout = $has_layout,
                sch.status = $status
            WITH sch
            MATCH (d:Document {id: $doc_id})
            MERGE (d)-[:CONTAINS_SCHEDULE]->(sch)
            """
            
            session.run(query, {
                'sch_id': sch_id,
                'name': schedule.name,
                'heading': schedule.heading,
                'content': schedule.content,
                'has_layout': schedule.layout is not None,
                'status': schedule.metadata.status or 'active',
                'doc_id': doc_id
            })
            
            count += 1
        
        return count

    def _create_reference_relationships(self, session, extracted_content: ExtractedContent) -> int:
        """Create reference relationships between documents."""
        count = 0

        # Process all references from all content types
        all_references = extracted_content.references.copy()

        # Add references from definitions
        for definition in extracted_content.definitions:
            all_references.extend(definition.references)

        # Add references from sections
        for section in extracted_content.sections:
            all_references.extend(section.references)

        # Add references from amendments
        for amendment in extracted_content.amendments:
            all_references.extend(amendment.references)

        doc_id = f"{extracted_content.document_metadata.doc_name}_{extracted_content.content_hash}"

        for reference in all_references:
            if reference.href and reference.href.startswith('/hk/'):
                # External reference to another HK document
                target_doc = self._extract_document_from_href(reference.href)
                if target_doc:
                    query = """
                    MATCH (source:Document {id: $source_id})
                    MERGE (target:Document {name: $target_name})
                    MERGE (source)-[:REFERENCES {type: $ref_type, text: $ref_text, href: $href}]->(target)
                    """

                    session.run(query, {
                        'source_id': doc_id,
                        'target_name': target_doc,
                        'ref_type': reference.type,
                        'ref_text': reference.text,
                        'href': reference.href
                    })
                    count += 1

        return count

    def _create_amendment_relationships(self, session, extracted_content: ExtractedContent) -> int:
        """Create amendment relationships."""
        count = 0
        doc_id = f"{extracted_content.document_metadata.doc_name}_{extracted_content.content_hash}"

        for amendment in extracted_content.amendments:
            if amendment.amending_document:
                query = """
                MATCH (target:Document {id: $target_id})
                MERGE (amending:Document {name: $amending_name})
                MERGE (amending)-[:AMENDS {
                    type: $amendment_type,
                    source_note: $source_note,
                    effective_date: $effective_date
                }]->(target)
                """

                session.run(query, {
                    'target_id': doc_id,
                    'amending_name': amendment.amending_document,
                    'amendment_type': amendment.amendment_type,
                    'source_note': amendment.source_note,
                    'effective_date': amendment.effective_date.isoformat() if amendment.effective_date else None
                })
                count += 1

        return count

    def _create_hierarchical_relationships(self, session, extracted_content: ExtractedContent) -> int:
        """Create hierarchical relationships within document."""
        count = 0
        doc_id = f"{extracted_content.document_metadata.doc_name}_{extracted_content.content_hash}"

        # Create part-section relationships
        query = """
        MATCH (d:Document {id: $doc_id})
        MATCH (s:Section)-[:CONTAINED_IN]->(d)
        WHERE s.hierarchy_path CONTAINS 'part'
        WITH s, split(s.hierarchy_path, ' > ') as hierarchy
        WHERE size(hierarchy) >= 2
        MERGE (p:Part {name: hierarchy[1], document_id: $doc_id})
        MERGE (p)-[:CONTAINS_SECTION]->(s)
        MERGE (d)-[:CONTAINS_PART]->(p)
        """

        try:
            result = session.run(query, {'doc_id': doc_id})
            count += result.consume().counters.relationships_created
        except Exception as e:
            logger.debug(f"No hierarchical relationships to create: {e}")

        return count

    def _extract_document_from_href(self, href: str) -> Optional[str]:
        """Extract document name from href like '/hk/cap442' or '/hk/1990/32'."""
        try:
            parts = href.strip('/').split('/')
            if len(parts) >= 2 and parts[0] == 'hk':
                if parts[1].startswith('cap'):
                    return f"Cap. {parts[1][3:]}"
                elif len(parts) >= 3:
                    return f"{parts[1]} of {parts[2]}"
                else:
                    return parts[1]
        except Exception:
            pass
        return None
