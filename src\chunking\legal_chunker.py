"""
Legal Document Chunking Strategies

This module provides specialized chunking strategies for legal documents
that preserve legal structure, context, and relationships.
"""
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from abc import ABC, abstractmethod

from ..processors.document_processor import ProcessedChunk, ProcessedDocument

logger = logging.getLogger(__name__)


@dataclass
class ChunkingConfig:
    """Configuration for chunking strategies"""
    max_chunk_size: int = 1000
    min_chunk_size: int = 100
    overlap_size: int = 200
    preserve_sections: bool = True
    preserve_definitions: bool = True
    preserve_citations: bool = True
    split_on_sentences: bool = True


class ChunkingStrategy(ABC):
    """Abstract base class for chunking strategies"""
    
    def __init__(self, config: ChunkingConfig):
        self.config = config
    
    @abstractmethod
    def chunk_document(self, document: ProcessedDocument) -> List[ProcessedChunk]:
        """Chunk a processed document"""
        pass


class HierarchicalLegalChunker(ChunkingStrategy):
    """
    Hierarchical chunking strategy that respects legal document structure.
    
    This chunker:
    1. Preserves legal hierarchy (parts, sections, subsections)
    2. Keeps definitions intact
    3. Maintains cross-references
    4. <PERSON><PERSON> overlapping for context preservation
    """
    
    def chunk_document(self, document: ProcessedDocument) -> List[ProcessedChunk]:
        """Chunk document while preserving legal structure"""
        chunks = []
        
        # Process existing chunks and potentially split them further
        for chunk in document.chunks:
            if self._needs_splitting(chunk):
                split_chunks = self._split_chunk(chunk)
                chunks.extend(split_chunks)
            else:
                chunks.append(chunk)
        
        # Add overlapping chunks for better context
        if self.config.overlap_size > 0:
            overlapping_chunks = self._create_overlapping_chunks(chunks)
            chunks.extend(overlapping_chunks)
        
        return chunks
    
    def _needs_splitting(self, chunk: ProcessedChunk) -> bool:
        """Determine if a chunk needs to be split"""
        if chunk.chunk_type in ['definition', 'title']:
            return False  # Never split definitions or titles
        
        return len(chunk.content) > self.config.max_chunk_size
    
    def _split_chunk(self, chunk: ProcessedChunk) -> List[ProcessedChunk]:
        """Split a large chunk into smaller ones"""
        if not self._needs_splitting(chunk):
            return [chunk]
        
        # Split by sentences first
        if self.config.split_on_sentences:
            sentences = self._split_into_sentences(chunk.content)
        else:
            # Fallback to word-based splitting
            words = chunk.content.split()
            sentences = [' '.join(words[i:i+50]) for i in range(0, len(words), 50)]
        
        # Group sentences into chunks
        sub_chunks = []
        current_chunk_content = []
        current_length = 0
        
        for sentence in sentences:
            sentence_length = len(sentence)
            
            # If adding this sentence would exceed max size, create a new chunk
            if current_length + sentence_length > self.config.max_chunk_size and current_chunk_content:
                sub_chunk = self._create_sub_chunk(chunk, ' '.join(current_chunk_content), len(sub_chunks))
                sub_chunks.append(sub_chunk)
                current_chunk_content = [sentence]
                current_length = sentence_length
            else:
                current_chunk_content.append(sentence)
                current_length += sentence_length
        
        # Add remaining content
        if current_chunk_content:
            sub_chunk = self._create_sub_chunk(chunk, ' '.join(current_chunk_content), len(sub_chunks))
            sub_chunks.append(sub_chunk)
        
        return sub_chunks if sub_chunks else [chunk]
    
    def _create_sub_chunk(self, original_chunk: ProcessedChunk, content: str, index: int) -> ProcessedChunk:
        """Create a sub-chunk from an original chunk"""
        sub_chunk_id = f"{original_chunk.id}_part_{index}"
        
        # Copy metadata and add sub-chunk info
        metadata = original_chunk.metadata.copy()
        metadata.update({
            'is_sub_chunk': True,
            'original_chunk_id': original_chunk.id,
            'sub_chunk_index': index
        })
        
        return ProcessedChunk(
            id=sub_chunk_id,
            content=content,
            metadata=metadata,
            section_hierarchy=original_chunk.section_hierarchy,
            chunk_type=original_chunk.chunk_type,
            parent_document_id=original_chunk.parent_document_id,
            section_id=original_chunk.section_id,
            legal_citations=self._extract_citations_from_content(content),
            cross_references=self._extract_references_from_content(content)
        )
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences (simple implementation)"""
        import re
        
        # Simple sentence splitting - can be improved with spaCy
        sentences = re.split(r'(?<=[.!?])\s+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _create_overlapping_chunks(self, chunks: List[ProcessedChunk]) -> List[ProcessedChunk]:
        """Create overlapping chunks for better context"""
        overlapping_chunks = []
        
        for i in range(len(chunks) - 1):
            current_chunk = chunks[i]
            next_chunk = chunks[i + 1]
            
            # Only create overlaps for content chunks in the same section
            if (current_chunk.chunk_type == 'content' and 
                next_chunk.chunk_type == 'content' and
                self._are_adjacent_sections(current_chunk, next_chunk)):
                
                overlap_chunk = self._create_overlap_chunk(current_chunk, next_chunk, i)
                if overlap_chunk:
                    overlapping_chunks.append(overlap_chunk)
        
        return overlapping_chunks
    
    def _are_adjacent_sections(self, chunk1: ProcessedChunk, chunk2: ProcessedChunk) -> bool:
        """Check if two chunks are from adjacent sections"""
        # Simple check - can be made more sophisticated
        return (chunk1.parent_document_id == chunk2.parent_document_id and
                len(chunk1.section_hierarchy) == len(chunk2.section_hierarchy))
    
    def _create_overlap_chunk(self, chunk1: ProcessedChunk, chunk2: ProcessedChunk, index: int) -> Optional[ProcessedChunk]:
        """Create an overlapping chunk between two chunks"""
        # Get last part of first chunk and first part of second chunk
        content1_words = chunk1.content.split()
        content2_words = chunk2.content.split()
        
        overlap_words = min(self.config.overlap_size // 2, len(content1_words), len(content2_words))
        
        if overlap_words < 10:  # Don't create very small overlaps
            return None
        
        overlap_content = ' '.join(content1_words[-overlap_words:] + content2_words[:overlap_words])
        
        overlap_id = f"overlap_{chunk1.parent_document_id}_{index}"
        
        # Combine metadata from both chunks
        metadata = chunk1.metadata.copy()
        metadata.update({
            'is_overlap_chunk': True,
            'source_chunks': [chunk1.id, chunk2.id],
            'chunk_type': 'overlap'
        })
        
        return ProcessedChunk(
            id=overlap_id,
            content=overlap_content,
            metadata=metadata,
            section_hierarchy=chunk1.section_hierarchy,
            chunk_type='overlap',
            parent_document_id=chunk1.parent_document_id,
            legal_citations=self._extract_citations_from_content(overlap_content),
            cross_references=self._extract_references_from_content(overlap_content)
        )
    
    def _extract_citations_from_content(self, content: str) -> List[str]:
        """Extract citations from content (placeholder implementation)"""
        import re
        citation_pattern = re.compile(r'(?:Cap\.|Chapter|Section|s\.|ss\.|Ordinance)\s*\d+[A-Z]*(?:\([^)]+\))?')
        return citation_pattern.findall(content)
    
    def _extract_references_from_content(self, content: str) -> List[str]:
        """Extract references from content (placeholder implementation)"""
        import re
        ref_pattern = re.compile(r'<ref[^>]*>([^<]*)</ref>')
        return ref_pattern.findall(content)


class SemanticLegalChunker(ChunkingStrategy):
    """
    Semantic chunking strategy that groups related legal concepts together.
    
    This chunker uses semantic similarity to group related content,
    while still respecting legal boundaries.
    """
    
    def chunk_document(self, document: ProcessedDocument) -> List[ProcessedChunk]:
        """Chunk document based on semantic similarity"""
        # This is a placeholder for semantic chunking
        # Would require embedding models and similarity calculations
        
        # For now, fall back to hierarchical chunking
        hierarchical_chunker = HierarchicalLegalChunker(self.config)
        return hierarchical_chunker.chunk_document(document)


class LegalChunkerFactory:
    """Factory for creating legal chunkers"""
    
    @staticmethod
    def create_chunker(strategy: str, config: ChunkingConfig) -> ChunkingStrategy:
        """Create a chunker based on strategy name"""
        if strategy == "hierarchical":
            return HierarchicalLegalChunker(config)
        elif strategy == "semantic":
            return SemanticLegalChunker(config)
        else:
            raise ValueError(f"Unknown chunking strategy: {strategy}")
    
    @staticmethod
    def get_default_config() -> ChunkingConfig:
        """Get default chunking configuration"""
        return ChunkingConfig(
            max_chunk_size=1000,
            min_chunk_size=100,
            overlap_size=200,
            preserve_sections=True,
            preserve_definitions=True,
            preserve_citations=True,
            split_on_sentences=True
        )
