"""FastAPI application for Hong Kong Legal RAG System."""

import logging
from contextlib import asynccontextmanager
from typing import Dict, List, Optional

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel

from ..config import settings
from ..pipeline.processing_pipeline import LegalDocumentPipeline
from ..rag.legal_rag_system import LegalRAGSystem

# Configure logging
logging.basicConfig(level=getattr(logging, settings.log_level))
logger = logging.getLogger(__name__)

# Global instances
pipeline = None
rag_system = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global pipeline, rag_system
    
    # Startup
    logger.info("Starting Hong Kong Legal RAG System")
    pipeline = LegalDocumentPipeline()
    rag_system = LegalRAGSystem(pipeline)
    
    yield
    
    # Shutdown
    logger.info("Shutting down Hong Kong Legal RAG System")
    if pipeline:
        pipeline.close()


# Create FastAPI app
app = FastAPI(
    title="Hong Kong Legal RAG System",
    description="AI-powered legal document search and analysis system for Hong Kong law",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Request/Response models
class ChatRequest(BaseModel):
    message: str
    conversation_history: Optional[List[Dict]] = None


class ChatResponseModel(BaseModel):
    message: str
    sources: List[Dict]
    confidence: float
    follow_up_questions: List[str]


class SearchRequest(BaseModel):
    query: str
    limit: int = 10
    filters: Optional[Dict] = None


class ProcessingRequest(BaseModel):
    directory_path: str
    force_reprocess: bool = False


class ProcessingStatus(BaseModel):
    status: str
    message: str
    stats: Optional[Dict] = None


# API Routes
@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Hong Kong Legal RAG System API",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "pipeline_ready": pipeline is not None,
        "rag_system_ready": rag_system is not None
    }


@app.post("/chat", response_model=ChatResponseModel)
async def chat(request: ChatRequest):
    """Chat with the legal AI assistant."""
    if not rag_system:
        raise HTTPException(status_code=503, detail="RAG system not initialized")
    
    try:
        # Convert conversation history if provided
        conversation_history = []
        if request.conversation_history:
            from ..models import ChatMessage
            for msg in request.conversation_history:
                conversation_history.append(ChatMessage(
                    role=msg.get('role', 'user'),
                    content=msg.get('content', ''),
                    metadata=msg.get('metadata', {})
                ))
        
        response = rag_system.chat(request.message, conversation_history)
        
        # Convert sources to dictionaries
        sources = []
        for source in response.sources:
            sources.append({
                'content': source.content,
                'content_type': source.content_type.value,
                'element_type': source.element_type.value,
                'document': source.document,
                'section': source.section,
                'score': source.score,
                'metadata': source.metadata
            })
        
        return ChatResponseModel(
            message=response.message,
            sources=sources,
            confidence=response.confidence,
            follow_up_questions=response.follow_up_questions
        )
        
    except Exception as e:
        logger.error(f"Chat error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/search")
async def search_documents(request: SearchRequest):
    """Search legal documents."""
    if not pipeline:
        raise HTTPException(status_code=503, detail="Pipeline not initialized")
    
    try:
        results = pipeline.search_documents(
            query=request.query,
            limit=request.limit,
            filters=request.filters
        )
        
        return {
            "query": request.query,
            "results": results,
            "total_results": len(results)
        }
        
    except Exception as e:
        logger.error(f"Search error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/process", response_model=ProcessingStatus)
async def process_documents(request: ProcessingRequest, background_tasks: BackgroundTasks):
    """Process legal documents from a directory."""
    if not pipeline:
        raise HTTPException(status_code=503, detail="Pipeline not initialized")
    
    try:
        # Run processing in background
        background_tasks.add_task(
            _process_documents_background,
            request.directory_path,
            request.force_reprocess
        )
        
        return ProcessingStatus(
            status="started",
            message=f"Processing started for directory: {request.directory_path}"
        )
        
    except Exception as e:
        logger.error(f"Processing error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _process_documents_background(directory_path: str, force_reprocess: bool):
    """Background task for document processing."""
    try:
        logger.info(f"Starting background processing of {directory_path}")
        stats = pipeline.process_directory(directory_path, force_reprocess)
        logger.info(f"Background processing completed: {stats}")
    except Exception as e:
        logger.error(f"Background processing failed: {e}")


@app.get("/stats")
async def get_statistics():
    """Get system statistics."""
    if not pipeline:
        raise HTTPException(status_code=503, detail="Pipeline not initialized")
    
    try:
        stats = pipeline.get_processing_stats()
        return stats
        
    except Exception as e:
        logger.error(f"Stats error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/documents/{document_name}/related")
async def get_related_documents(document_name: str):
    """Get documents related to the specified document."""
    if not pipeline:
        raise HTTPException(status_code=503, detail="Pipeline not initialized")
    
    try:
        related = pipeline.find_related_documents(document_name)
        return {
            "document": document_name,
            "related_documents": related
        }
        
    except Exception as e:
        logger.error(f"Related documents error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/definitions/{definition_name}/usage")
async def get_definition_usage(definition_name: str):
    """Get usage information for a legal definition."""
    if not pipeline:
        raise HTTPException(status_code=503, detail="Pipeline not initialized")
    
    try:
        usage = pipeline.find_definition_usage(definition_name)
        return {
            "definition": definition_name,
            "usage_info": usage
        }
        
    except Exception as e:
        logger.error(f"Definition usage error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/data")
async def clear_all_data():
    """Clear all processed data (use with caution)."""
    if not pipeline:
        raise HTTPException(status_code=503, detail="Pipeline not initialized")
    
    try:
        pipeline.clear_all_data()
        return {"message": "All data cleared successfully"}
        
    except Exception as e:
        logger.error(f"Clear data error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/export/report")
async def export_processing_report():
    """Export a processing report."""
    if not pipeline:
        raise HTTPException(status_code=503, detail="Pipeline not initialized")
    
    try:
        from datetime import datetime
        output_path = f"processing_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        pipeline.export_processing_report(output_path)
        
        return {
            "message": "Report exported successfully",
            "file_path": output_path
        }
        
    except Exception as e:
        logger.error(f"Export report error: {e}")
        raise HTTPException(status_code=500, detail=str(e))


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.api.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )
