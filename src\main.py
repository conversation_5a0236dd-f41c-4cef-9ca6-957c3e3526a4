"""
Main processing script for Hong Kong Legal Documents RAG System

This script orchestrates the entire pipeline:
1. Parse XML legal documents
2. Process and chunk documents
3. Create embeddings and store in vector database
4. Provide basic search functionality
"""
import logging
import sys
from pathlib import Path
from typing import List, Optional
import click
from tqdm import tqdm

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from src.parsers.hklm_parser import HKLMParser, LegalDocument
from src.processors.document_processor import DocumentProcessor
from src.chunking.legal_chunker import LegalChunkerFactory, ChunkingConfig
from src.vectorstore.vector_manager import LegalVectorStore, VectorStoreManager
from config.settings import settings

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format=settings.LOG_FORMAT
)
logger = logging.getLogger(__name__)


class LegalRAGPipeline:
    """Main pipeline for processing legal documents"""
    
    def __init__(self):
        self.parser = HKLMParser()
        self.processor = DocumentProcessor(
            chunk_size=settings.CHUNK_SIZE,
            chunk_overlap=settings.CHUNK_OVERLAP
        )
        self.chunker = LegalChunkerFactory.create_chunker(
            "hierarchical", 
            LegalChunkerFactory.get_default_config()
        )
        self.vector_manager = VectorStoreManager()
    
    def process_directory(self, directory_path: Path, collection_name: str = "legal_documents") -> bool:
        """Process all XML files in a directory"""
        try:
            logger.info(f"Starting processing of directory: {directory_path}")
            
            # Step 1: Parse XML documents
            logger.info("Step 1: Parsing XML documents...")
            documents = self.parser.parse_directory(directory_path)
            
            if not documents:
                logger.error("No documents were successfully parsed")
                return False
            
            logger.info(f"Successfully parsed {len(documents)} documents")
            
            # Step 2: Process documents
            logger.info("Step 2: Processing documents...")
            processed_docs = []
            
            for doc in tqdm(documents, desc="Processing documents"):
                try:
                    processed_doc = self.processor.process_document(doc)
                    processed_docs.append(processed_doc)
                except Exception as e:
                    logger.error(f"Failed to process document: {e}")
                    continue
            
            if not processed_docs:
                logger.error("No documents were successfully processed")
                return False
            
            # Step 3: Apply chunking strategy
            logger.info("Step 3: Applying chunking strategy...")
            final_chunks = []
            
            for doc in tqdm(processed_docs, desc="Chunking documents"):
                try:
                    chunks = self.chunker.chunk_document(doc)
                    # Update the document with new chunks
                    doc.chunks = chunks
                    final_chunks.extend(chunks)
                except Exception as e:
                    logger.error(f"Failed to chunk document {doc.title}: {e}")
                    continue
            
            logger.info(f"Generated {len(final_chunks)} total chunks")
            
            # Step 4: Store in vector database
            logger.info("Step 4: Storing in vector database...")
            vector_store = self.vector_manager.get_store(collection_name)
            
            added_count = vector_store.add_documents(processed_docs)
            logger.info(f"Added {added_count} chunks to vector store")
            
            # Step 5: Get processing statistics
            stats = self.processor.get_processing_stats(processed_docs)
            logger.info(f"Processing complete. Stats: {stats}")
            
            return True
            
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            return False
    
    def search_documents(self, query: str, collection_name: str = "legal_documents", k: int = 5):
        """Search documents in the vector store"""
        try:
            vector_store = self.vector_manager.get_store(collection_name)
            results = vector_store.search_similar(query, k=k)
            
            print(f"\nSearch Results for: '{query}'\n" + "="*50)
            
            for i, (doc, score) in enumerate(results, 1):
                print(f"\n{i}. Score: {score:.4f}")
                print(f"Document: {doc.metadata.get('document_title', 'Unknown')}")
                print(f"Section: {doc.metadata.get('section_hierarchy', 'N/A')}")
                print(f"Type: {doc.metadata.get('chunk_type', 'Unknown')}")
                print(f"Content: {doc.page_content[:200]}...")
                print("-" * 50)
            
            return results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            return []


@click.group()
def cli():
    """Hong Kong Legal Documents RAG System"""
    pass


@cli.command()
@click.option('--directory', '-d', type=click.Path(exists=True), 
              help='Directory containing XML legal documents')
@click.option('--collection', '-c', default='legal_documents',
              help='Vector store collection name')
def process(directory: str, collection: str):
    """Process legal documents and build vector index"""
    if not directory:
        # Use default directories from settings
        directories = [
            settings.XML_LEGISLATION_DIR,
            settings.XML_INSTRUMENTS_DIR
        ]
    else:
        directories = [Path(directory)]
    
    pipeline = LegalRAGPipeline()
    
    for dir_path in directories:
        if dir_path.exists():
            logger.info(f"Processing directory: {dir_path}")
            success = pipeline.process_directory(dir_path, collection)
            if success:
                logger.info(f"Successfully processed {dir_path}")
            else:
                logger.error(f"Failed to process {dir_path}")
        else:
            logger.warning(f"Directory not found: {dir_path}")


@cli.command()
@click.option('--query', '-q', required=True, help='Search query')
@click.option('--collection', '-c', default='legal_documents',
              help='Vector store collection name')
@click.option('--limit', '-l', default=5, help='Number of results to return')
def search(query: str, collection: str, limit: int):
    """Search legal documents"""
    pipeline = LegalRAGPipeline()
    results = pipeline.search_documents(query, collection, limit)
    
    if not results:
        print("No results found.")


@cli.command()
@click.option('--collection', '-c', default='legal_documents',
              help='Vector store collection name')
def stats(collection: str):
    """Show vector store statistics"""
    vector_manager = VectorStoreManager()
    vector_store = vector_manager.get_store(collection)
    
    stats = vector_store.get_collection_stats()
    
    print(f"\nVector Store Statistics for '{collection}'")
    print("=" * 50)
    for key, value in stats.items():
        print(f"{key}: {value}")


@cli.command()
@click.option('--collection', '-c', default='legal_documents',
              help='Vector store collection name')
@click.confirmation_option(prompt='Are you sure you want to clear the collection?')
def clear(collection: str):
    """Clear vector store collection"""
    vector_manager = VectorStoreManager()
    vector_store = vector_manager.get_store(collection)
    
    if vector_store.clear_collection():
        print(f"Successfully cleared collection '{collection}'")
    else:
        print(f"Failed to clear collection '{collection}'")


if __name__ == "__main__":
    cli()
