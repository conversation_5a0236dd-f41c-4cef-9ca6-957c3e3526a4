"""Main processing pipeline for Hong Kong Legal XML documents."""

import logging
from pathlib import Path
from typing import Dict, List, Optional

from ..config import settings
from ..graph.relationship_manager import LegalRelationshipManager
from ..parsers.comprehensive_parser import ComprehensiveHKLMParser
from ..vectorstore.comprehensive_vector_manager import ComprehensiveVectorManager

logger = logging.getLogger(__name__)


class LegalDocumentPipeline:
    """Main pipeline for processing Hong Kong legal documents."""
    
    def __init__(self):
        self.parser = ComprehensiveHKLMParser()
        self.vector_manager = ComprehensiveVectorManager()
        self.relationship_manager = LegalRelationshipManager()
        
        self.processed_documents = {}
        self.processing_stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0
        }
    
    def process_directory(self, directory_path: str, force_reprocess: bool = False) -> Dict[str, int]:
        """Process all XML files in a directory."""
        directory = Path(directory_path)
        if not directory.exists():
            raise ValueError(f"Directory does not exist: {directory_path}")
        
        xml_files = list(directory.rglob("*.xml"))
        logger.info(f"Found {len(xml_files)} XML files in {directory_path}")
        
        total_stats = {
            'files_processed': 0,
            'definitions_indexed': 0,
            'sections_indexed': 0,
            'schedules_indexed': 0,
            'amendments_indexed': 0,
            'references_indexed': 0,
            'relationships_created': 0
        }
        
        for xml_file in xml_files:
            try:
                result = self.process_file(xml_file, force_reprocess)
                if result:
                    total_stats['files_processed'] += 1
                    for key in ['definitions_indexed', 'sections_indexed', 'schedules_indexed', 
                               'amendments_indexed', 'references_indexed']:
                        if key.replace('_indexed', '') in result['vector_stats']:
                            total_stats[key] += result['vector_stats'][key.replace('_indexed', '')]
                    
                    for key in ['definitions', 'sections', 'schedules', 'amendments', 'references']:
                        if key in result['relationship_stats']:
                            total_stats['relationships_created'] += result['relationship_stats'][key]
                    
                    self.processing_stats['successful'] += 1
                else:
                    self.processing_stats['skipped'] += 1
                    
            except Exception as e:
                logger.error(f"Failed to process {xml_file}: {e}")
                self.processing_stats['failed'] += 1
                continue
            
            self.processing_stats['total_processed'] += 1
        
        logger.info(f"Processing complete. Stats: {total_stats}")
        logger.info(f"Processing summary: {self.processing_stats}")
        
        return total_stats
    
    def process_file(self, file_path: str, force_reprocess: bool = False) -> Optional[Dict]:
        """Process a single XML file."""
        file_path = Path(file_path)
        
        if not file_path.exists():
            logger.error(f"File does not exist: {file_path}")
            return None
        
        # Check if already processed
        if not force_reprocess and str(file_path) in self.processed_documents:
            logger.debug(f"Skipping already processed file: {file_path}")
            return None
        
        try:
            # Parse the document
            logger.info(f"Parsing document: {file_path}")
            extracted_content = self.parser.parse_file(file_path)
            
            if not extracted_content:
                logger.warning(f"No content extracted from {file_path}")
                return None
            
            # Index in vector database
            logger.info(f"Indexing in vector database: {file_path}")
            vector_stats = self.vector_manager.index_document(extracted_content)
            
            # Index relationships in graph database
            logger.info(f"Indexing relationships: {file_path}")
            relationship_stats = {}
            if self.relationship_manager.driver:
                relationship_stats = self.relationship_manager.index_document_relationships(extracted_content)
            else:
                logger.warning("Graph database not available, skipping relationship indexing")
            
            # Store processing record
            self.processed_documents[str(file_path)] = {
                'content_hash': extracted_content.content_hash,
                'extracted_at': extracted_content.extracted_at,
                'vector_stats': vector_stats,
                'relationship_stats': relationship_stats
            }
            
            logger.info(f"Successfully processed {file_path}")
            
            return {
                'file_path': str(file_path),
                'content_hash': extracted_content.content_hash,
                'vector_stats': vector_stats,
                'relationship_stats': relationship_stats,
                'extracted_content': extracted_content
            }
            
        except Exception as e:
            logger.error(f"Error processing {file_path}: {e}")
            raise
    
    def reprocess_document(self, file_path: str) -> Optional[Dict]:
        """Reprocess a document (force reprocessing)."""
        return self.process_file(file_path, force_reprocess=True)
    
    def get_processing_stats(self) -> Dict:
        """Get comprehensive processing statistics."""
        stats = {
            'processing_stats': self.processing_stats.copy(),
            'vector_stats': self.vector_manager.get_collection_stats(),
            'processed_documents': len(self.processed_documents)
        }
        
        # Add graph database stats if available
        if self.relationship_manager.driver:
            stats['graph_stats'] = self.relationship_manager.get_document_statistics()
        
        return stats
    
    def search_documents(self, query: str, limit: int = 10, filters: Dict = None) -> List[Dict]:
        """Search across all indexed documents."""
        from ..models import SearchQuery
        
        search_query = SearchQuery(
            query=query,
            limit=limit,
            filters=filters or {}
        )
        
        results = self.vector_manager.search(search_query)
        
        # Convert SearchResult objects to dictionaries for JSON serialization
        formatted_results = []
        for result in results:
            formatted_result = {
                'content': result.content,
                'content_type': result.content_type.value,
                'element_type': result.element_type.value,
                'document': result.document,
                'section': result.section,
                'score': result.score,
                'metadata': result.metadata
            }
            formatted_results.append(formatted_result)
        
        return formatted_results
    
    def find_related_documents(self, document_name: str) -> List[Dict]:
        """Find documents related to the given document."""
        if not self.relationship_manager.driver:
            logger.warning("Graph database not available")
            return []
        
        return self.relationship_manager.find_related_documents(document_name)
    
    def find_definition_usage(self, definition_name: str) -> List[Dict]:
        """Find where a definition is used across documents."""
        if not self.relationship_manager.driver:
            logger.warning("Graph database not available")
            return []
        
        return self.relationship_manager.find_definition_usage(definition_name)
    
    def clear_all_data(self):
        """Clear all processed data (use with caution)."""
        logger.warning("Clearing all processed data")
        
        # Clear vector database
        self.vector_manager.clear_all_collections()
        
        # Clear graph database
        if self.relationship_manager.driver:
            with self.relationship_manager.driver.session() as session:
                session.run("MATCH (n) DETACH DELETE n")
        
        # Clear processing records
        self.processed_documents.clear()
        self.processing_stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'skipped': 0
        }
        
        logger.info("All data cleared")
    
    def export_processing_report(self, output_path: str):
        """Export a comprehensive processing report."""
        import json
        from datetime import datetime
        
        report = {
            'generated_at': datetime.now().isoformat(),
            'processing_stats': self.get_processing_stats(),
            'processed_documents': {
                path: {
                    'content_hash': info['content_hash'],
                    'extracted_at': info['extracted_at'].isoformat(),
                    'vector_stats': info['vector_stats'],
                    'relationship_stats': info['relationship_stats']
                }
                for path, info in self.processed_documents.items()
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Processing report exported to {output_path}")
    
    def close(self):
        """Close all connections."""
        if self.relationship_manager:
            self.relationship_manager.close()
        
        logger.info("Pipeline closed")
