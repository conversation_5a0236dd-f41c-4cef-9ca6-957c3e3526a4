"""OpenAI embeddings service for Hong Kong Legal RAG System."""

import logging
import time
from typing import List, Optional, Union

import openai
from openai import OpenAI

from ..config import settings

logger = logging.getLogger(__name__)


class OpenAIEmbeddingService:
    """OpenAI embeddings service with rate limiting and error handling."""
    
    def __init__(self, 
                 api_key: Optional[str] = None,
                 model: str = "text-embedding-ada-002",
                 max_retries: int = 3,
                 retry_delay: float = 1.0):
        """
        Initialize OpenAI embedding service.
        
        Args:
            api_key: OpenAI API key (uses settings if not provided)
            model: Embedding model to use
            max_retries: Maximum number of retries for failed requests
            retry_delay: Delay between retries in seconds
        """
        self.api_key = api_key or settings.openai_api_key
        self.model = model
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
        if not self.api_key:
            raise ValueError("OpenAI API key is required")
        
        # Initialize OpenAI client
        self.client = OpenAI(api_key=self.api_key)
        
        # Rate limiting
        self.last_request_time = 0
        self.min_request_interval = 0.1  # 100ms between requests
        
        # Model specifications
        self.model_specs = {
            "text-embedding-ada-002": {
                "max_tokens": 8191,
                "dimensions": 1536,
                "cost_per_1k_tokens": 0.0001
            },
            "text-embedding-3-small": {
                "max_tokens": 8191,
                "dimensions": 1536,
                "cost_per_1k_tokens": 0.00002
            },
            "text-embedding-3-large": {
                "max_tokens": 8191,
                "dimensions": 3072,
                "cost_per_1k_tokens": 0.00013
            }
        }
        
        self.current_spec = self.model_specs.get(model, self.model_specs["text-embedding-ada-002"])
        
        logger.info(f"Initialized OpenAI embedding service with model: {model}")
    
    def get_embedding(self, text: str) -> List[float]:
        """
        Get embedding for a single text.
        
        Args:
            text: Text to embed
            
        Returns:
            List of embedding values
        """
        if not text or not text.strip():
            logger.warning("Empty text provided for embedding")
            return [0.0] * self.current_spec["dimensions"]
        
        # Truncate text if too long
        text = self._truncate_text(text)
        
        for attempt in range(self.max_retries):
            try:
                # Rate limiting
                self._rate_limit()
                
                # Make API request
                response = self.client.embeddings.create(
                    model=self.model,
                    input=text,
                    encoding_format="float"
                )
                
                # Extract embedding
                embedding = response.data[0].embedding
                
                logger.debug(f"Generated embedding for text of length {len(text)}")
                return embedding
                
            except openai.RateLimitError as e:
                logger.warning(f"Rate limit hit, attempt {attempt + 1}/{self.max_retries}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay * (2 ** attempt))  # Exponential backoff
                else:
                    logger.error(f"Rate limit exceeded after {self.max_retries} attempts")
                    raise
                    
            except openai.APIError as e:
                logger.error(f"OpenAI API error: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                else:
                    raise
                    
            except Exception as e:
                logger.error(f"Unexpected error generating embedding: {e}")
                if attempt < self.max_retries - 1:
                    time.sleep(self.retry_delay)
                else:
                    # Return zero vector as fallback
                    logger.warning("Returning zero vector as fallback")
                    return [0.0] * self.current_spec["dimensions"]
        
        # Should not reach here, but return zero vector as ultimate fallback
        return [0.0] * self.current_spec["dimensions"]
    
    def get_embeddings_batch(self, texts: List[str], batch_size: int = 100) -> List[List[float]]:
        """
        Get embeddings for multiple texts in batches.
        
        Args:
            texts: List of texts to embed
            batch_size: Number of texts to process in each batch
            
        Returns:
            List of embedding lists
        """
        if not texts:
            return []
        
        all_embeddings = []
        
        # Process in batches
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i + batch_size]
            
            # Filter out empty texts
            valid_texts = [text for text in batch if text and text.strip()]
            if not valid_texts:
                # Add zero vectors for empty batch
                all_embeddings.extend([[0.0] * self.current_spec["dimensions"]] * len(batch))
                continue
            
            # Truncate texts if needed
            truncated_texts = [self._truncate_text(text) for text in valid_texts]
            
            for attempt in range(self.max_retries):
                try:
                    # Rate limiting
                    self._rate_limit()
                    
                    # Make batch API request
                    response = self.client.embeddings.create(
                        model=self.model,
                        input=truncated_texts,
                        encoding_format="float"
                    )
                    
                    # Extract embeddings
                    batch_embeddings = [item.embedding for item in response.data]
                    
                    # Handle case where some texts in original batch were empty
                    result_embeddings = []
                    valid_idx = 0
                    
                    for original_text in batch:
                        if original_text and original_text.strip():
                            result_embeddings.append(batch_embeddings[valid_idx])
                            valid_idx += 1
                        else:
                            result_embeddings.append([0.0] * self.current_spec["dimensions"])
                    
                    all_embeddings.extend(result_embeddings)
                    
                    logger.debug(f"Generated embeddings for batch of {len(batch)} texts")
                    break
                    
                except openai.RateLimitError as e:
                    logger.warning(f"Rate limit hit for batch, attempt {attempt + 1}/{self.max_retries}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay * (2 ** attempt))
                    else:
                        logger.error(f"Rate limit exceeded for batch after {self.max_retries} attempts")
                        # Add zero vectors for failed batch
                        all_embeddings.extend([[0.0] * self.current_spec["dimensions"]] * len(batch))
                        
                except openai.APIError as e:
                    logger.error(f"OpenAI API error for batch: {e}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        # Add zero vectors for failed batch
                        all_embeddings.extend([[0.0] * self.current_spec["dimensions"]] * len(batch))
                        
                except Exception as e:
                    logger.error(f"Unexpected error for batch: {e}")
                    if attempt < self.max_retries - 1:
                        time.sleep(self.retry_delay)
                    else:
                        # Add zero vectors for failed batch
                        all_embeddings.extend([[0.0] * self.current_spec["dimensions"]] * len(batch))
        
        return all_embeddings
    
    def _truncate_text(self, text: str) -> str:
        """Truncate text to fit within model token limits."""
        max_tokens = self.current_spec["max_tokens"]
        
        # Rough estimation: 1 token ≈ 4 characters for English
        # For legal text, use more conservative estimate
        max_chars = max_tokens * 3
        
        if len(text) <= max_chars:
            return text
        
        # Truncate and add indicator
        truncated = text[:max_chars - 20] + "... [truncated]"
        logger.debug(f"Truncated text from {len(text)} to {len(truncated)} characters")
        
        return truncated
    
    def _rate_limit(self):
        """Implement simple rate limiting."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    def get_model_info(self) -> dict:
        """Get information about the current model."""
        return {
            "model": self.model,
            "max_tokens": self.current_spec["max_tokens"],
            "dimensions": self.current_spec["dimensions"],
            "cost_per_1k_tokens": self.current_spec["cost_per_1k_tokens"]
        }
    
    def estimate_cost(self, texts: Union[str, List[str]]) -> float:
        """Estimate the cost for embedding given texts."""
        if isinstance(texts, str):
            texts = [texts]
        
        total_chars = sum(len(text) for text in texts if text)
        # Rough token estimation
        estimated_tokens = total_chars / 3
        
        cost_per_token = self.current_spec["cost_per_1k_tokens"] / 1000
        estimated_cost = estimated_tokens * cost_per_token
        
        return estimated_cost
    
    def health_check(self) -> dict:
        """Check if the OpenAI embedding service is working."""
        try:
            # Test with a simple embedding
            test_embedding = self.get_embedding("test")
            
            return {
                "status": "healthy",
                "model": self.model,
                "dimensions": len(test_embedding),
                "api_key_configured": bool(self.api_key)
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "model": self.model,
                "api_key_configured": bool(self.api_key)
            }
