"""Qdrant vector database manager with OpenAI embeddings for Hong Kong Legal RAG System."""

import logging
import uuid
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import (
    CollectionStatus, Distance, FieldCondition, Filter, MatchValue, 
    PointStruct, VectorParams
)

from ..config import settings
from ..models import ContentType, ElementType, ExtractedContent, SearchQuery, SearchResult
from .openai_embeddings import OpenAIEmbeddingService

logger = logging.getLogger(__name__)


class QdrantVectorManager:
    """Production-ready Qdrant vector database manager with OpenAI embeddings."""
    
    def __init__(self, 
                 host: str = "localhost",
                 port: int = 6333,
                 api_key: Optional[str] = None,
                 use_openai_embeddings: bool = True):
        """
        Initialize Qdrant vector manager.
        
        Args:
            host: Qdrant server host
            port: Qdrant server port  
            api_key: Qdrant Cloud API key (optional)
            use_openai_embeddings: Whether to use OpenAI embeddings
        """
        self.host = host
        self.port = port
        self.api_key = api_key
        
        # Initialize Qdrant client
        if api_key:
            # Qdrant Cloud
            self.client = QdrantClient(
                url=f"https://{host}",
                api_key=api_key,
                timeout=60
            )
        else:
            # Local Qdrant
            self.client = QdrantClient(
                host=host,
                port=port,
                timeout=60
            )
        
        # Initialize embedding service
        if use_openai_embeddings and settings.openai_api_key:
            self.embedding_service = OpenAIEmbeddingService()
            self.embedding_dimension = 1536  # OpenAI text-embedding-ada-002 dimension
        else:
            # Fallback to sentence transformers
            from sentence_transformers import SentenceTransformer
            self.embedding_model = SentenceTransformer(settings.embedding_model)
            self.embedding_dimension = self.embedding_model.get_sentence_embedding_dimension()
            self.embedding_service = None
        
        # Collection configurations
        self.collections = {
            'definitions': 'hk_legal_definitions',
            'sections': 'hk_legal_sections', 
            'schedules': 'hk_legal_schedules',
            'amendments': 'hk_legal_amendments',
            'references': 'hk_legal_references',
            'general': 'hk_legal_general'
        }
        
        # Initialize collections
        self._initialize_collections()
        
        logger.info(f"Initialized Qdrant manager with {len(self.collections)} collections")
    
    def _initialize_collections(self):
        """Initialize all required collections."""
        for collection_type, collection_name in self.collections.items():
            try:
                # Check if collection exists
                collections = self.client.get_collections()
                existing_names = [col.name for col in collections.collections]
                
                if collection_name not in existing_names:
                    # Create collection with optimized settings
                    self.client.create_collection(
                        collection_name=collection_name,
                        vectors_config=VectorParams(
                            size=self.embedding_dimension,
                            distance=Distance.COSINE,
                            on_disk=True  # Store vectors on disk for large datasets
                        ),
                        optimizers_config=models.OptimizersConfig(
                            default_segment_number=2,
                            max_segment_size=20000,
                            memmap_threshold=20000,
                            indexing_threshold=20000,
                            flush_interval_sec=5,
                            max_optimization_threads=2
                        ),
                        hnsw_config=models.HnswConfig(
                            m=16,
                            ef_construct=100,
                            full_scan_threshold=10000,
                            max_indexing_threads=2
                        )
                    )
                    logger.info(f"Created collection: {collection_name}")
                else:
                    logger.info(f"Collection already exists: {collection_name}")
                    
            except Exception as e:
                logger.error(f"Failed to initialize collection {collection_name}: {e}")
    
    def get_embedding(self, text: str, language: str = "en") -> List[float]:
        """Generate embedding for text."""
        try:
            if self.embedding_service:
                # Use OpenAI embeddings
                return self.embedding_service.get_embedding(text)
            else:
                # Use sentence transformers
                embedding = self.embedding_model.encode(text, convert_to_tensor=False)
                return embedding.tolist()
        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            # Return zero vector as fallback
            return [0.0] * self.embedding_dimension
    
    def add_definitions(self, extracted_content: ExtractedContent):
        """Add legal definitions to vector database."""
        if not extracted_content.definitions:
            return
        
        collection_name = self.collections['definitions']
        points = []
        
        for definition in extracted_content.definitions:
            try:
                # Prepare text for embedding
                text_parts = []
                if definition.name:
                    text_parts.append(f"Definition: {definition.name}")
                
                if definition.terms:
                    for term in definition.terms:
                        if term.text:
                            text_parts.append(term.text)
                
                if definition.content:
                    text_parts.append(definition.content)
                
                full_text = " ".join(text_parts)
                if not full_text.strip():
                    continue
                
                # Generate embedding
                embedding = self.get_embedding(full_text, extracted_content.document_metadata.language)
                
                # Prepare metadata
                metadata = {
                    "document": extracted_content.document_metadata.doc_name,
                    "title": extracted_content.document_metadata.title,
                    "language": extracted_content.document_metadata.language,
                    "element_type": "definition",
                    "definition_name": definition.name or "",
                    "section_id": definition.section_id or "",
                    "content_type": "legal_definition",
                    "priority": self._get_content_priority("definition"),
                    "document_type": extracted_content.document_type.value,
                    "effective_date": extracted_content.document_metadata.effective_date.isoformat() if extracted_content.document_metadata.effective_date else None
                }
                
                # Create point
                point = PointStruct(
                    id=str(uuid.uuid4()),
                    vector=embedding,
                    payload={
                        "content": full_text,
                        **metadata
                    }
                )
                points.append(point)
                
            except Exception as e:
                logger.error(f"Failed to process definition {definition.name}: {e}")
                continue
        
        # Batch insert points
        if points:
            try:
                self.client.upsert(
                    collection_name=collection_name,
                    points=points
                )
                logger.info(f"Added {len(points)} definitions to {collection_name}")
            except Exception as e:
                logger.error(f"Failed to insert definitions: {e}")
    
    def add_sections(self, extracted_content: ExtractedContent):
        """Add legal sections to vector database."""
        if not extracted_content.sections:
            return
        
        collection_name = self.collections['sections']
        points = []
        
        for section in extracted_content.sections:
            try:
                # Prepare text for embedding
                text_parts = []
                if section.heading:
                    text_parts.append(f"Section {section.section_id}: {section.heading}")
                
                if section.content:
                    text_parts.append(section.content)
                
                # Add subsections
                for subsection in section.subsections:
                    if subsection.content:
                        text_parts.append(f"Subsection {subsection.subsection_id}: {subsection.content}")
                
                full_text = " ".join(text_parts)
                if not full_text.strip():
                    continue
                
                # Generate embedding
                embedding = self.get_embedding(full_text, extracted_content.document_metadata.language)
                
                # Prepare metadata
                metadata = {
                    "document": extracted_content.document_metadata.doc_name,
                    "title": extracted_content.document_metadata.title,
                    "language": extracted_content.document_metadata.language,
                    "element_type": "section",
                    "section_id": section.section_id,
                    "heading": section.heading or "",
                    "content_type": "legal_section",
                    "priority": self._get_content_priority("section"),
                    "document_type": extracted_content.document_type.value,
                    "subsection_count": len(section.subsections),
                    "effective_date": extracted_content.document_metadata.effective_date.isoformat() if extracted_content.document_metadata.effective_date else None
                }
                
                # Create point
                point = PointStruct(
                    id=str(uuid.uuid4()),
                    vector=embedding,
                    payload={
                        "content": full_text,
                        **metadata
                    }
                )
                points.append(point)
                
            except Exception as e:
                logger.error(f"Failed to process section {section.section_id}: {e}")
                continue
        
        # Batch insert points
        if points:
            try:
                self.client.upsert(
                    collection_name=collection_name,
                    points=points
                )
                logger.info(f"Added {len(points)} sections to {collection_name}")
            except Exception as e:
                logger.error(f"Failed to insert sections: {e}")
    
    def add_schedules(self, extracted_content: ExtractedContent):
        """Add legal schedules to vector database."""
        if not extracted_content.schedules:
            return
        
        collection_name = self.collections['schedules']
        points = []
        
        for schedule in extracted_content.schedules:
            try:
                # Prepare text for embedding
                text_parts = []
                if schedule.title:
                    text_parts.append(f"Schedule {schedule.schedule_id}: {schedule.title}")
                
                if schedule.content:
                    text_parts.append(schedule.content)
                
                # Add table data if present
                if hasattr(schedule, 'table_data') and schedule.table_data:
                    for row in schedule.table_data[:5]:  # Limit to first 5 rows
                        text_parts.append(" | ".join(str(cell) for cell in row))
                
                full_text = " ".join(text_parts)
                if not full_text.strip():
                    continue
                
                # Generate embedding
                embedding = self.get_embedding(full_text, extracted_content.document_metadata.language)
                
                # Prepare metadata
                metadata = {
                    "document": extracted_content.document_metadata.doc_name,
                    "title": extracted_content.document_metadata.title,
                    "language": extracted_content.document_metadata.language,
                    "element_type": "schedule",
                    "schedule_id": schedule.schedule_id,
                    "schedule_title": schedule.title or "",
                    "content_type": "legal_schedule",
                    "priority": self._get_content_priority("schedule"),
                    "document_type": extracted_content.document_type.value,
                    "effective_date": extracted_content.document_metadata.effective_date.isoformat() if extracted_content.document_metadata.effective_date else None
                }
                
                # Create point
                point = PointStruct(
                    id=str(uuid.uuid4()),
                    vector=embedding,
                    payload={
                        "content": full_text,
                        **metadata
                    }
                )
                points.append(point)
                
            except Exception as e:
                logger.error(f"Failed to process schedule {schedule.schedule_id}: {e}")
                continue
        
        # Batch insert points
        if points:
            try:
                self.client.upsert(
                    collection_name=collection_name,
                    points=points
                )
                logger.info(f"Added {len(points)} schedules to {collection_name}")
            except Exception as e:
                logger.error(f"Failed to insert schedules: {e}")
    
    def _get_content_priority(self, content_type: str) -> int:
        """Get priority score for content type."""
        priorities = {
            "definition": 10,
            "section": 8,
            "schedule": 6,
            "amendment": 7,
            "reference": 5
        }
        return priorities.get(content_type, 5)

    def search(self, query: SearchQuery) -> List[SearchResult]:
        """Search across all collections with advanced filtering."""
        all_results = []

        # Generate query embedding
        query_embedding = self.get_embedding(query.query, query.language)

        # Search each collection type based on query preferences
        collections_to_search = []

        if query.include_definitions:
            collections_to_search.append(('definitions', self.collections['definitions']))
        if query.include_sections:
            collections_to_search.append(('sections', self.collections['sections']))
        if query.include_schedules:
            collections_to_search.append(('schedules', self.collections['schedules']))

        # If no specific types requested, search all
        if not collections_to_search:
            collections_to_search = [(k, v) for k, v in self.collections.items()]

        for collection_type, collection_name in collections_to_search:
            try:
                # Build filter conditions
                filter_conditions = []

                if query.document_types:
                    filter_conditions.append(
                        FieldCondition(
                            key="document_type",
                            match=MatchValue(value=query.document_types[0])
                        )
                    )

                if query.language:
                    filter_conditions.append(
                        FieldCondition(
                            key="language",
                            match=MatchValue(value=query.language)
                        )
                    )

                if query.date_range:
                    # Add date range filtering if needed
                    pass

                # Create filter
                search_filter = Filter(must=filter_conditions) if filter_conditions else None

                # Perform search
                search_results = self.client.search(
                    collection_name=collection_name,
                    query_vector=query_embedding,
                    query_filter=search_filter,
                    limit=query.limit // len(collections_to_search) + 1,
                    score_threshold=query.min_score
                )

                # Convert to SearchResult objects
                for result in search_results:
                    search_result = SearchResult(
                        content=result.payload.get("content", ""),
                        score=float(result.score),
                        document=result.payload.get("document", ""),
                        section=result.payload.get("section_id", ""),
                        element_type=ElementType(result.payload.get("element_type", "section")),
                        metadata=result.payload
                    )
                    all_results.append(search_result)

            except Exception as e:
                logger.error(f"Search failed for collection {collection_name}: {e}")
                continue

        # Sort by score and return top results
        all_results.sort(key=lambda x: x.score, reverse=True)
        return all_results[:query.limit]

    def search_by_metadata(self, filters: Dict[str, Any], limit: int = 10) -> List[Dict]:
        """Search by metadata filters across all collections."""
        all_results = []

        for collection_name in self.collections.values():
            try:
                # Build filter conditions
                filter_conditions = []
                for key, value in filters.items():
                    filter_conditions.append(
                        FieldCondition(
                            key=key,
                            match=MatchValue(value=value)
                        )
                    )

                search_filter = Filter(must=filter_conditions)

                # Scroll through all matching points
                scroll_result = self.client.scroll(
                    collection_name=collection_name,
                    scroll_filter=search_filter,
                    limit=limit,
                    with_payload=True,
                    with_vectors=False
                )

                for point in scroll_result[0]:
                    all_results.append({
                        "id": point.id,
                        "content": point.payload.get("content", ""),
                        "metadata": point.payload
                    })

            except Exception as e:
                logger.error(f"Metadata search failed for {collection_name}: {e}")
                continue

        return all_results[:limit]

    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics for all collections."""
        stats = {}

        for collection_type, collection_name in self.collections.items():
            try:
                collection_info = self.client.get_collection(collection_name)
                stats[collection_type] = {
                    "name": collection_name,
                    "count": collection_info.points_count,
                    "status": collection_info.status.value,
                    "vectors_count": collection_info.vectors_count,
                    "indexed_vectors_count": collection_info.indexed_vectors_count
                }
            except Exception as e:
                logger.error(f"Failed to get stats for {collection_name}: {e}")
                stats[collection_type] = {"error": str(e)}

        return stats

    def delete_collection(self, collection_type: str) -> bool:
        """Delete a collection."""
        if collection_type not in self.collections:
            logger.error(f"Unknown collection type: {collection_type}")
            return False

        collection_name = self.collections[collection_type]

        try:
            self.client.delete_collection(collection_name)
            logger.info(f"Deleted collection: {collection_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete collection {collection_name}: {e}")
            return False

    def recreate_collection(self, collection_type: str) -> bool:
        """Recreate a collection (delete and create new)."""
        if collection_type not in self.collections:
            logger.error(f"Unknown collection type: {collection_type}")
            return False

        collection_name = self.collections[collection_type]

        try:
            # Delete existing collection
            try:
                self.client.delete_collection(collection_name)
            except Exception:
                pass  # Collection might not exist

            # Create new collection
            self.client.create_collection(
                collection_name=collection_name,
                vectors_config=VectorParams(
                    size=self.embedding_dimension,
                    distance=Distance.COSINE,
                    on_disk=True
                )
            )

            logger.info(f"Recreated collection: {collection_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to recreate collection {collection_name}: {e}")
            return False

    def health_check(self) -> Dict[str, Any]:
        """Check Qdrant cluster health."""
        try:
            collections = self.client.get_collections()

            health_info = {
                "status": "healthy",
                "collections_count": len(collections.collections),
                "collections": [col.name for col in collections.collections],
                "embedding_service": "openai" if self.embedding_service else "sentence_transformers",
                "embedding_dimension": self.embedding_dimension
            }

            return health_info

        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }

    def close(self):
        """Close Qdrant client connection."""
        try:
            if hasattr(self.client, 'close'):
                self.client.close()
            logger.info("Closed Qdrant client connection")
        except Exception as e:
            logger.error(f"Error closing Qdrant client: {e}")
